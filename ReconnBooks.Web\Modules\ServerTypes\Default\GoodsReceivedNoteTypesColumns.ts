﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { GoodsReceivedNoteTypesRow } from "./GoodsReceivedNoteTypesRow";

export interface GoodsReceivedNoteTypesColumns {
    RowNumber: Column<GoodsReceivedNoteTypesRow>;
    GoodsReceivedNoteTypeName: Column<GoodsReceivedNoteTypesRow>;
    Description: Column<GoodsReceivedNoteTypesRow>;
    GoodsReceivedNoteTypeId: Column<GoodsReceivedNoteTypesRow>;
}

export class GoodsReceivedNoteTypesColumns extends ColumnsBase<GoodsReceivedNoteTypesRow> {
    static readonly columnsKey = 'Default.GoodsReceivedNoteTypes';
    static readonly Fields = fieldsProxy<GoodsReceivedNoteTypesColumns>();
}