﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";
import { GoodsReceivedNoteDetailsRow } from "./GoodsReceivedNoteDetailsRow";

export interface GoodsReceivedNotesRow {
    RowNumber?: number;
    GoodsReceivedNoteId?: number;
    GoodsReceivedNoteNo?: string;
    GoodsReceivedNoteDate?: string;
    GoodsReceivedNoteMonth?: string;
    VendorId?: number;
    GoodsReceivedNoteTypeId?: number;
    GSTIN?: string;
    VendorEMailId?: string;
    PurchaseOrderId?: number;
    FinancialYearId?: number;
    FinancialYearName?: string;
    VendorDcInvoiceNo?: string;
    VendorDcInvoiceDate?: string;
    GoodsReceivedNoteDetailsList?: GoodsReceivedNoteDetailsRow[];
    ShippedThrough?: string;
    ShippingDocketNo?: string;
    DeliveryAddress?: string;
    DeliveryCityId?: number;
    DeliveryPinCode?: number;
    VehicleNo?: string;
    VehicleType?: string;
    GatePassNo?: string;
    GatePassDate?: string;
    Inspection?: string;
    ReceivedByEmployeeId?: number;
    UploadDocuments?: string;
    Remarks?: string;
    ClientId?: number;
    ClientName?: string;
    PreparedByUserId?: number;
    PreparedDate?: string;
    VerifiedByUserId?: number;
    VerifiedDate?: string;
    AuthorizedByUserId?: number;
    AuthorizedDate?: string;
    ModifiedByUserId?: number;
    ModifiedDate?: string;
    CancelledByUserId?: number;
    CancelledDate?: string;
    AuthorizedStatus?: boolean;
    GoodsReceivedNoteTypeName?: string;
    VendorName?: string;
    PurchaseOrderNo?: string;
    DeliveryCityCityName?: string;
    ReceivedByEmployeeEmployeeName?: string;
    PreparedByUserUsername?: string;
    VerifiedByUserUsername?: string;
    AuthorizedByUserUsername?: string;
    ModifiedByUserUsername?: string;
    CancelledByUserUsername?: string;
}

export abstract class GoodsReceivedNotesRow {
    static readonly idProperty = 'GoodsReceivedNoteId';
    static readonly nameProperty = 'GoodsReceivedNoteNo';
    static readonly localTextPrefix = 'Default.GoodsReceivedNotes';
    static readonly lookupKey = 'Default.GoodsReceivedNotes';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<GoodsReceivedNotesRow>('Default.GoodsReceivedNotes') }
    static async getLookupAsync() { return getLookupAsync<GoodsReceivedNotesRow>('Default.GoodsReceivedNotes') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<GoodsReceivedNotesRow>();
}