﻿using Microsoft.AspNetCore.Mvc;
using Serenity.Web;

namespace ReconnBooks.Default.Pages;

[PageAuthorize(typeof(GoodsReceivedNoteTypesRow))]
public class GoodsReceivedNoteTypesPage : Controller
{
    [Route("Default/GoodsReceivedNoteTypes")]
    public ActionResult Index()
    {
        return this.GridPage("@/Default/GoodsReceivedNoteTypes/GoodsReceivedNoteTypesPage",
            GoodsReceivedNoteTypesRow.Fields.PageTitle());
    }
}