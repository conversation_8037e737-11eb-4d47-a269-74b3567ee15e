﻿import { StringEditor, TextAreaEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";

export interface GoodsReceivedNoteTypesForm {
    GoodsReceivedNoteTypeName: StringEditor;
    Description: TextAreaEditor;
}

export class GoodsReceivedNoteTypesForm extends PrefixedContext {
    static readonly formKey = 'Default.GoodsReceivedNoteTypes';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!GoodsReceivedNoteTypesForm.init)  {
            GoodsReceivedNoteTypesForm.init = true;

            var w0 = StringEditor;
            var w1 = TextAreaEditor;

            initFormType(GoodsReceivedNoteTypesForm, [
                'GoodsReceivedNoteTypeName', w0,
                'Description', w1
            ]);
        }
    }
}