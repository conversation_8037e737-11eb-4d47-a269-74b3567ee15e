/*Glassy theme background changed to a differnt image*/
.theme-glassy-light .content {
  padding: 7px !important;
}

/*--------------Fixed bottom Sidebar----------------------*/
  .fixed-text {
    white-space: nowrap;
    overflow: hidden;
    max-width: 100%;
    display: inline-block;
    vertical-align: middle;
  }

  .dropdown-menu {
    min-width: 200px;
  }

  .s-user-avatar img {
    width: 32px;
    height: 32px;
    border-radius: 5%;
    object-fit: cover;
  }

  .clickable-text:hover {
    color: #007eff;
  }

.arrow {
  font-size: 16px;
  transition: font-size 0.2s ease-in-out;
  z-index: 1000;
  pointer-events: auto;
}

    .arrow:hover {
      font-size: 17px !important; 
    }

  .user-initials {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: #6c757d;
    color: white;
    font-size: 16px;
    font-weight: bold;
  }

  .s-sidebar-user-info {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 5px;
    text-align: center;
    border-top: 1px solid #ddd;
    box-sizing: border-box;
  }

  .custom-font-style {
    font-family: var(--bs-body-font-family);
    color: var(--s-sidebar-link);
    font-size: 1.1rem;
    font-weight: 500;
    letter-spacing: 0.01em;
    text-transform: none;
    line-height: 1.8rem;
  }
/*----------Fixed bottom Sidebar--------------*/

/*-----------------Dashboard------------------*/
  .s-dashboard-card-sm {
    position: relative;
  }

  .s-dashboard-card-sm .card-body {
    position: relative;
    z-index: 2;
  }

  .s-dashboard-card-sm h3 {
    position: relative;
    z-index: 3;
  }

  .s-dashboard-card-sm .icon {
    display: none;
  }

  #orders-by-quarter-pane canvas {
    height: 345px !important; 
    max-height: 400px;
  }

  .s-dashboard-card.s-dashboard-todo {
    height: 525px; 
    display: flex;
    flex-direction: column;
  }

  .s-dashboard-card.s-dashboard-todo .card-body {
    flex: 1;
    max-height: 525px; 
    overflow-y: auto; 
  }

  .flatpickr-calendar.open, .flatpickr-calendar.inline {
    max-height: 408px;
    height: 408px;
  }

/*-----------------------------------*/

.slick-header-column.no-header-filter .s-header-filter-button {
  display: none !important;
}

.s-site-logo-img {
  content: url(../../Serenity.Assets/logo/white-128.png);
}

.s-UserPermissionDialog > .size,
.s-RolePermissionDialog > .size {
  min-width: 770px;
  width: 770px;
  min-height: 550px;
  height: 550px;
}

.s-UserPermissionDialog .s-DataGrid,
.s-RolePermissionDialog .s-DataGrid {
  height: 100%;
}

.s-horizontal-divider {
  margin: 1em 0;
  line-height: 0;
  text-align: center;
}

  .s-horizontal-divider span {
    background-color: var(--s-card-bg);
    padding: 1em;
  }

  .s-horizontal-divider:before {
    content: " ";
    display: block;
    border-top: 1px solid #e3e3e3;
    border-bottom: 1px solid #f7f7f7;
  }

/*---To make the details grid expandable---*/

.s-QuotationDetailsGridEditor .grid-container,
.s-SalesOrderDetailsGridEditor .grid-container,
.s-DeliveryNoteDetailsGridEditor .grid-container,
.s-ProformaInvoiceDetailsGridEditor .grid-container,
.s-InvoiceDetailsGridEditor .grid-container,
.s-SalesReturnDetailsGridEditor .grid-container,
.s-ReceiptDetailsGridEditor .grid-container,
.s-CreditNoteDetailsGridEditor .grid-container,
.s-PurchaseOrderDetailsGridEditor .grid-container,
.s-PoAmendmentDetailsGridEditor .grid-container,
.s-GoodsReceivedNoteDetailsGridEditor .grid-container,
.s-VendorBillDetailsGridEditor .grid-container,
.s-VendorPaymentDetailsGridEditor .grid-container,
.s-PurchaseReturnDetailsGridEditor .grid-container,
.s-DebitNoteDetailsGridEditor .grid-container {
  min-height: 150px;
  max-height: none;
  height: 150px; /* default height */
  resize: vertical; /* allow vertical resizing */
  overflow: auto; /* ensures scrollbars if content overflows */
}

/*-----------------Master Settings------------------*/

.s-AddressedToDialog .size,
.s-BanksDialog .size,
.s-BusinessCategoriesDialog .size,
.s-BusinessGroupsDialog .size,
.s-BusinessTypesDialog .size,
.s-CountriesDialog .size,
.s-StatesDialog .size,
.s-DepartmentsDialog .size,
.s-DesignationsDialog .size,
.s-DocumentsDialog .size,
.s-FinancialYearsDialog .size,
.s-GstRatesDialog .size,
.s-GstReturnsDialog .size,
.s-ModeOfPaymentsDialog .size,
.s-PaymentTermsDialog .size,
.s-ReturnsFilingFormsDialog .size,
.s-TitlesDialog .size,
.s-DistrictsDialog .size,
.s-CitiesDialog .size,
.s-ClientUsersDialog .size,
.s-ConsultantsDialog .size,
.s-CustomersDialog .size,
.s-CustomerContactsDialog .size,
.s-ProductTypesDialog .size,
.s-ProductGroupsDialog .size,
.s-ProductCategoriesDialog .size,
.s-ProductMakeDialog .size,
.s-UnitsDialog .size,
.s-UqCsDialog .size,
.s-SupplySectionsDialog .size,
.s-NatureOfSupplyDialog .size,
.s-UserTypesDialog .size {
  width: 650px;
}
/*---------------*/

.s-UserDialog       .caption

{
  width: 170px;
  text-align: left;
}


/*---------------*/
.s-RejectionReasonsDialog .size,
.s-ReplacementMethodsDialog .size,
.s-FootNotesDialog .size,
.s-HeaderNoteDialog .size,
.s-NarrationsDialog .size,
.s-HeaderNotesDialog .size,
.s-EmployeesDialog .size {
  width: 850px;
  padding: 2px;
}

/*---------------*/

.s-ReplacementMethodsDialog .caption
{
  width: 180px;
  text-align: left;
}

/*------Left alignment for captions------*/
.field .caption {
  text-align: left;
}
  /*--Dialog Caption Width and Text align-left Setting for all module forms-------*/

.s-BanksDialog                  .caption,
.s-BusinessCategoriesDialog     .caption,
.s-DepartmentsDialog            .caption,
.s-DocumentsDialog              .caption,
.s-AddressedToDialog            .caption,
.s-BusinessGroupsDialog         .caption,
.s-BusinessTypesDialog          .caption,
.s-CountriesDialog              .caption,
.s-StatesDialog                 .caption,
.s-DesignationsDialog           .caption,
.s-FinancialYearsDialog         .caption,
.s-GstRatesDialog               .caption,
.s-GstReturnsDialog             .caption,
.s-PaymentTermsDialog           .caption,
.s-TitlesDialog                 .caption,
.s-UserTypesDialog              .caption,
.s-DistrictsDialog              .caption,
.s-CitiesDialog                 .caption,
.s-ClientsDialog                .caption,
.s-ClientBankAccountsDialog     .caption,
.s-ClientUsersDialog            .caption,
.s-ConsultantsDialog            .caption,
.s-HeaderNoteDialog             .caption,
.s-FootNotesDialog              .caption,
.s-NarrationsDialog             .caption,
.s-ConsultantUsersDialog        .caption,
.s-HsnsacCodesDialog            .caption,
.s-CustomersDialog              .caption,
.s-EmployeesDialog              .caption,
.s-QuotationsDialog             .caption,
.s-QuotationDetailsDialog       .caption,
.s-SalesOrdersDialog            .caption,
.s-SalesOrderDetailsDialog      .caption,
.s-DeliveryNotesDialog          .caption,
.s-DeliveryNoteDetailsDialog    .caption,
.s-ProformaInvoicesDialog       .caption,

.s-ProformaInvoiceDetailsDialog .caption,
.s-InvoicesDialog               .caption,
.s-InvoiceDetailsDialog         .caption,
.s-ReceiptsDialog               .caption,
.s-ReceiptDetailsDialog         .caption,
.s-SalesReturnsDialog           .caption,
.s-SalesReturnDetailsDialog     .caption,
.s-GoodsReceivedNotesDialog                   .caption,
.s-GoodsReceivedNoteDetailsDialog             .caption,
.s-VendorBillsDialog            .caption,
.s-VendorsDialog                .caption,
.s-VendorContactsDialog         .caption,
.s-PurchaseOrderDetailsDialog   .caption,
.s-CustomerContactsDialog       .caption,
.s-ProductTypesDialog           .caption,
.s-ProductGroupsDialog          .caption,
.s-ProductCategoriesDialog      .caption,
.s-ProductMakeDialog            .caption,
.s-StatesDialog                 .caption,
.s-RacksDialog                  .caption,
.s-LocationsDialog              .caption,
.s-WarehousesDialog             .caption,
.s-GoodsReceivedNoteTypesDialog               .caption,

.s-CommodityTypesDialog         .caption
{
  width: 140px;
  text-align: left;
}
/*---------------*/

.s-RejectionReasonsDialog       .caption,
.s-NatureOfSupplyDialog         .caption,
.s-NatureOfSupplySectionDialog  .caption,
.s-ReturnsFilingFormsDialog     .caption,
.s-ModeOfPaymentsDialog         .caption,
.s-NatureOfSupplyDialog         .caption,
.s-SupplySectionsDialog         .caption,
.s-UnitsDialog                  .caption,
.s-UqCsDialog                   .caption,
.s-TdsRatesDialog               .caption,
.s-TcsRatesDialog               .caption,
.s-SalesReturnDetailsDialog     .caption,
.s-PurchaseOrdersDialog         .caption 

{
  width: 150px;
  text-align: left;
}
/*---------------*/

.s-PrimaryGroupsDialog      .caption,
.s-SecondaryGroupsDialog    .caption,
.s-LedgerAccountsDialog     .caption,
.s-OpeningBalancesDialog    .caption,
{
  width: 180px;
  text-align: left;
}
/*---------------*/

/* The following code is to put "*" symbol for mandatory fields at the Right Side */
.s-ProductCategoriesDialog    .field .caption,
.s-UnitsDialog                .field .caption,
.s-UqCsDialog                 .field .caption,
.s-ProductTypesDialog         .field .caption,
.s-ProductGroupsDialog        .field .caption,
.s-ProductCategoriesDialog    .field .caption,
.s-ProductMakeDialog          .field .caption,
.s-CommodityTypesDialog       .field .caption,
.s-HsnsacCodesDialog          .field .caption,

.s-GstRatesDialog             .field .caption,
.s-TdsRatesDialog             .field .caption,
.s-TcsRatesDialog             .field .caption,
.s-SupplyTypesDialog          .field .caption,
.s-ReturnsFilingFormsDialog   .field .caption,

.s-CountriesDialog            .field .caption,
.s-StatesDialog               .field .caption,
.s-DistrictsDialog            .field .caption,
.s-CitiesDialog               .field .caption,
.s-TitlesDialog               .field .caption,
.s-DesignationsDialog         .field .caption,
.s-DepartmentsDialog          .field .caption,
.s-AddressedToDialog          .field .caption,
.s-DocumentsDialog            .field .caption,
.s-BanksDialog                .field .caption,
.s-FinancialYearsDialog       .field .caption,

.s-PaymentTermsDialog         .field .caption,
.s-ModeOfPaymentsDialog       .field .caption,
.s-HeaderNoteDialog           .field .caption,
.s-FootNotesDialog            .field .caption,
.s-NarrationsDialog           .field .caption,
.s-ReplacementMethodsDialog   .field .caption,
.s-RejectionReasonsDialog     .field .caption,
.s-FeedbacksDialog            .field .caption,

.s-StoresDialog               .field .caption,
.s-RacksDialog                .field .caption,
.s-LocationsDialog            .field .caption,
.s-WarehousesDialog           .field .caption,
.s-GoodsReceivedNoteTypesDialog             .field .caption,

.s-EmployeesDialog            .field .caption,
.s-BusinessCategoriesDialog   .field .caption,
.s-BusinessGroupsDialog       .field .caption,
.s-BusinessTypesDialog        .field .caption

{
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}
/*---------------*/

.s-ClientsDialog 
{
  .ClientCode input, 
  .ClientName input, 
  .ConsultantId input
      {
        font-weight: bold;
      }
}

/*----- Users Dialog Category formatting ----- */
.s-TdsRatesDialog 
{
  .TDSRate input, 
  .Section input
      {
        font-weight: bold;
      }
}
/*---------------*/

/*----- Users Dialog Category formatting ----- */
.s-UserDialog .category-title {
  flex-wrap: wrap;
}

  .s-UserDialog .category-title a {
    color: #d99000;
  }

  .s-UserDialog .category-title::after {
    margin-top: 2px;
    margin-bottom: 10px;
    flex-shrink: 0;
    display: block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

.s-UserDialog .field .caption {
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}

/*----- Consultants Dialog Category formatting ----- */
.s-ConsultantsDialog .category-title {
  flex-wrap: wrap;
}

  .s-ConsultantsDialog .category-title a {
    color: #d99000;
  }

  .s-ConsultantsDialog .category-title::after {
    margin-top: 2px;
    margin-bottom: 2px;
    flex-shrink: 0;
    display: block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

.s-ConsultantsDialog .field .caption {
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}

.field.ConsultantBankAccountsList label.caption {
  display: none;
}

/*----- Consultant's Bank Accounts Category formatting ----- */
.s-ConsultantBankAccountsDialog .category-title {
  flex-wrap: wrap;
}

  .s-ConsultantBankAccountsDialog .category-title a {
    color: #d99000;
  }

  .s-ConsultantBankAccountsDialog .category-title::after {
    margin-top: 2px;
    margin-bottom: 2px;
    flex-shrink: 0;
    display: block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

.s-ConsultantBankAccountsDialog .field .caption {
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}

/*----- Clients Dialog Category formatting ----- */
.s-ClientsDialog .category-title {
  flex-wrap: wrap;
}

  .s-ClientsDialog .category-title a {
    color: #d99000;
  }

  .s-ClientsDialog .category-title::before {
    display:;
  }

  .s-ClientsDialog .category-title::after {
    margin-top: 2px;
    margin-bottom: 2px;
    flex-shrink: 0;
    display: inline-block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

.s-ClientsDialog .field .caption {
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}


.field.ClientBankAccountsList label.caption {
  display: none;
}

.s-ClientsDialog {
  .ClientCode input, .ClientName input, .ConsultantId input

{
  font-weight: bold;
}

}

/*----- Client's Bank Accounts Category formatting ----- */
.s-ClientBankAccountsDialog .category-title {
  flex-wrap: wrap;
}

  .s-ClientBankAccountsDialog .category-title a {
    color: #d99000;
  }

  .s-ClientBankAccountsDialog .category-title::after {
    margin-top: 2px;
    margin-bottom: 2px;
    flex-shrink: 0;
    display: block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

.s-ClientBankAccountsDialog .field .caption {
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}

.s-ClientsGrid .slick-header-columns {
  white-space: normal;
  overflow: hidden;
  height: auto !important;
  align-items: center; /* Vertically centers the text */
}

.s-ClientsGrid .slick-header-column {
  overflow: visible;
  height: auto;
}

.s-ClientsGrid .slick-header {
  padding: 0;
  background-color: aliceblue;
}

.s-ClientsGrid .slick-column-name {
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  line-height: 20px;
  max-height: 40px; /* Maximum height for two lines */
  min-height: 30px; /* Minimum height for a single line */
  word-wrap: break-word;
}

/*----- Commodities Dialong Category formatting ----- */
.s-CommoditiesDialog .category-title {
  flex-wrap: wrap;
}

  .s-CommoditiesDialog .category-title a {
    color: #d99000;
  }

  .s-CommoditiesDialog .category-title::after {
    margin-top: 2px;
    margin-bottom: 2px;
    flex-shrink: 0;
    display: block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

.s-CommoditiesDialog .field .caption {
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}

.s-CommoditiesDialog 
{
    .CommodityName input

        {
          font-weight: bold;
        }
}

.s-CommoditiesGrid .slick-header-columns {
  white-space: normal;
  overflow: hidden;
  height: auto !important;
  align-items: center; /* Vertically centers the text */
}

.s-CommoditiesGrid .slick-header-column {
  overflow: visible;
  height: auto;
}

.s-CommoditiesGrid .slick-header {
  padding: 0;
  background-color: aliceblue;
}

.s-CommoditiesGrid .slick-column-name {
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  line-height: 20px;
  max-height: 40px; /* Maximum height for two lines */
  min-height: 30px; /* Minimum height for a single line */
  word-wrap: break-word;
}
/*----- Customers Dialog Category formatting ----- */
.s-CustomersDialog .category-title {
  flex-wrap: wrap;
}

  .s-CustomersDialog .category-title a {
    color: #d99000;
  }

  .s-CustomersDialog .category-title::before {
    display:;
  }

  .s-CustomersDialog .category-title::after {
    margin-top: 2px;
    margin-bottom: 2px;
    flex-shrink: 0;
    display: inline-block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

.s-CustomersDialog .field .caption {
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}

.s-CustomersDialog 
{
  .CompanyName input
    {
      font-weight: bold;
    }
}

/*----- Customer Contacts Category formatting ----- */

.field.CustomerContactsList label.caption {
  display: none;
}

.s-CustomerContactsDialog .field .caption {
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}

.s-CustomerContactsDialog .size {
  width: 750px;
  padding: 2px;
}

.s-CustomerContactsDialog {
  .ContactName input

{
  font-weight: bold;
}

}
.s-CustomersGrid .slick-header-columns {
  white-space: normal;
  overflow: hidden;
  height: auto !important;
  align-items: center; /* Vertically centers the text */
}

.s-CustomersGrid .slick-header-column {
  overflow: visible;
  height: auto;
}

.s-CustomersGrid .slick-header {
  padding: 0;
  background-color: aliceblue;
}

.s-CustomersGrid .slick-column-name {
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  line-height: 20px;
  max-height: 40px; /* Maximum height for two lines */
  min-height: 30px; /* Minimum height for a single line */
  word-wrap: break-word;
}

/*----- QuotationsDialog Category formatting ----- */
.s-QuotationsDialog .category-title {
  flex-wrap: wrap;
}

.s-QuotationsDialog .category-title a {
    color: #d99000;
  }

.s-QuotationsDialog .category-title::before {
    display:;
  }

  .s-QuotationsDialog .category-title::after {
    margin-top: 2px;
    margin-bottom: 2px;
    flex-shrink: 0;
    display: inline-block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

.s-QuotationsDialog .field .caption {
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}


.field.QuotationDetailsList label.caption {
  display: none;
}

/*This will set the QuotationDetailsGrid Height to Minimum and Max height of the grid.*/
.s-QuotationsDialog .s-DataGrid {
  min-height: 299px;
  max-height: none;
}

.s-QuotationsDialog {
  .QuotationNo input, .GrandTotal input

{
  font-weight: bold;
}

.GrandTotal input {
  text-align: right;
}
}
/*--------------Grid UI Changes--------------*/
.s-QuotationsDialog .s-DataGrid {
  padding: 5px;
}

.s-QuotationsDialog .slick-header-columns {
  white-space: break-spaces;
  word-wrap: break-word;
  overflow: visible;
  min-height: 46px;
  height: auto !important;
}

.s-QuotationsDialog .slick-header {
  padding: 0;
  background-color: aliceblue;
}

.s-QuotationsDialog .slick-column-name{
    line-height: 20px;
}
/*------------------------------------------*/

/*----- QuotationDetailsDialog Category formatting ----- */
.s-QuotationDetailsDialog .category-title {
  flex-wrap: wrap;
}

  .s-QuotationDetailsDialog .category-title a {
    color: #d99000;
  }

  .s-QuotationDetailsDialog .category-title::before {
    display:;
  }

  .s-QuotationDetailsDialog .category-title::after {
    margin-top: 2px;
    margin-bottom: 2px;
    flex-shrink: 0;
    display: inline-block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

.s-QuotationDetailsDialog .field .caption {
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}

.s-QuotationsDialog .s-DataGrid {
  min-height: 299px;
  max-height: none;
}

.s-QuotationDetailsDialog 
{
   .UnitPrice input, 
   .Amount input, 
   .DiscountAmountPerUnit input, 
   .DiscountAmount input, 
   .TaxableAmountPerUnit input, 
   .NetTaxableAmount input, 
   .PerUnitIGSTAmount input, 
   .NetIGSTAmount input, 
   .PerUnitCGSTAmount input, 
   .NetCGSTAmount input, 
   .PerUnitSGSTAmount input, 
   .NetSGSTAmount input, 
   .PerUnitPrice input, 
   .NetAmount input

    {
      text-align: right
    }

  .Quantity input,
  .UnitPrice input,
  .NetTaxableAmount input,
  .PerUnitPrice input,
  .NetAmount input 
    {
      font-weight: bold;
    }
}

    /*------Gird UI changes------*/
.s-QuotationsGrid .slick-header-columns {
  white-space: normal;
  overflow: hidden;
  height: auto !important;
  align-items: center; /* Vertically centers the text */
}

.s-QuotationsGrid .slick-header-column {
  overflow: visible;
  height: auto;
}

.s-QuotationsGrid .slick-header {
  padding: 0;
  background-color: aliceblue;
}

.s-QuotationsGrid .slick-column-name {
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  line-height: 20px;
  max-height: 40px; /* Maximum height for two lines */
  min-height: 30px; /* Minimum height for a single line */
  word-wrap: break-word;
}

/*----------------------*/
/*To set the panel width to 900px*/  
@media (min-width: 992px) 
{
  .s-QuotationDetailsDialog .modal 
  {
    --bs-modal-width: 900px;  /*Adjust the modal width variable*/ 
    position: absolute;       /*Position it absolutely to enable movement*/ 
    cursor: move;             /*Change the cursor to indicate drag*/ 
  }

  .s-QuotationDetailsDialog .modal-dialog 
  {
    max-width: 900px;       /*Ensure dialog-specific max-width */
    margin: auto;           /*Center the dialog */
  }
}


/*END To set the panel width to 900px*/  
/*---------------*/


/*----- SalesOrdersDialog Category formatting ----- */
.s-SalesOrdersDialog .category-title {
  flex-wrap: wrap;
}

  .s-SalesOrdersDialog .category-title a {
    color: #d99000;
  }

  .s-SalesOrdersDialog .category-title::before {
    display:;
  }

  .s-SalesOrdersDialog .category-title::after {
    margin-top: 2px;
    margin-bottom: 2px;
    flex-shrink: 0;
    display: inline-block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

.s-SalesOrdersDialog .field .caption {
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}

.s-SalesOrdersDialog {
  .SalesOrderNo input, .GrandTotal input

{
  font-weight: bold;
}

.GrandTotal input {
  text-align: right;
}

}

.s-SalesOrdersDialog .s-DataGrid {
  padding: 5px;
}

.s-SalesOrdersDialog .slick-header-columns {
  white-space: break-spaces;
  word-wrap: break-word;
  overflow: visible;
  min-height: 46px;
  height: auto !important;
}

.s-SalesOrdersDialog .slick-header {
  padding: 0;
  background-color: aliceblue;
}

.s-SalesOrdersDialog .slick-column-name {
  line-height: 20px;
}

/*----- SalesOrderDetailsDialong Category formatting ----- */
.s-SalesOrderDetailsDialog .category-title {
  flex-wrap: wrap;
}

  .s-SalesOrderDetailsDialog .category-title a {
    color: #d99000;
  }

  .s-SalesOrderDetailsDialog .category-title::before {
    display:;
  }

  .s-SalesOrderDetailsDialog .category-title::after {
    margin-top: 2px;
    margin-bottom: 2px;
    flex-shrink: 0;
    display: inline-block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

.s-SalesOrderDetailsDialog .field .caption {
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}

.field.SalesOrderDetailsList label.caption {
  display: none;
}

/*This will set the SalesOrdersDetailsGrid Height to Minimum and Max height of the grid.*/
.s-SalesOrdersDialog .s-DataGrid {
  min-height: 299px;
  max-height: none;
}

.s-SalesOrderDetailsDialog {
      .OfferPrice input, 
      .OrderUnitPrice input, 
      .OrderUnitAmount input, 
      .DiscountAmountPerUnit input, 
      .NetDiscountAmount input, 
      .TaxableAmountPerUnit input, 
      .NetTaxableAmount input, 
      .IGSTAmountPerUnit input, 
      .NetIGSTAmount input, 
      .CGSTAmountPerUnit input, 
      .NetCGSTAmount input, 
      .SGSTAmountPerUnit input, 
      .NetSGSTAmount input, 
      .PricePerUnit input, 
      .NetAmount input

      {
        text-align: right;
      }

      .OrderQuantity input,
      .OrderUnitPrice input,
      .OrderUnitAmount input,
      .PricePerUnit input,
      .NetTaxableAmount input,
      .NetAmount input 

      {
        font-weight: bold;
      }
}

.s-SalesOrdersGrid .slick-header-columns {
  white-space: normal;
  overflow: hidden;
  height: auto !important;
  align-items: center; /* Vertically centers the text */
}

.s-SalesOrdersGrid .slick-header-column {
  overflow: visible;
  height: auto;
}

.s-SalesOrdersGrid .slick-header {
  padding: 0;
  background-color: aliceblue;
}

.s-SalesOrdersGrid .slick-column-name {
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  line-height: 20px;
  max-height: 40px; /* Maximum height for two lines */
  min-height: 30px; /* Minimum height for a single line */
  word-wrap: break-word;
}
/*----------------------*/
/*To set the panel width to 900px*/  
@media (min-width: 992px) 
{
  .s-SalesOrderDetailsDialog .modal 
  {
    --bs-modal-width: 900px;
    position: absolute;
    cursor: move;
  }

  .s-SalesOrderDetailsDialog .modal-dialog 
  {
    max-width: 900px;
    margin: auto;
  }
}
/*END To set the panel width to 900px*/  
/*---------------*/

/*----- DeliveryNotesDialog Category formatting ----- */
.s-DeliveryNotesDialog .category-title {
  flex-wrap: wrap;
}

  .s-DeliveryNotesDialog .category-title a {
    color: #d99000;
  }

  .s-DeliveryNotesDialog .category-title::before {
    display:;
  }

  .s-DeliveryNotesDialog .category-title::after {
    margin-top: 2px;
    margin-bottom: 2px;
    flex-shrink: 0;
    display: inline-block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

.s-DeliveryNotesDialog .field .caption {
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}

.field.DeliveryNoteDetailsList label.caption {
  display: none;
}

/*This will set the DetailsGrid Height to Minimum and Max height of the grid.*/
.s-DeliveryNotesDialog .s-DataGrid {
  min-height: 299px;
  max-height: none;
}

.s-DeliveryNotesDialog {
  .DeliveryNoteNo input

{
  font-weight: bold;
}

}

.s-DeliveryNotesDialog .s-DataGrid {
  padding: 5px;
}

.s-DeliveryNotesDialog .slick-header-columns {
  white-space: break-spaces;
  word-wrap: break-word;
  overflow: visible;
  /*min-height: 46px;*/
  height: auto !important;
}

.s-DeliveryNotesDialog .slick-header {
  padding: 0;
  background-color: aliceblue;
}

.s-DeliveryNotesDialog .slick-column-name {
  line-height: 20px;
}
/*----- DeliveryNoteDetailsDialong Category formatting ----- */
.s-DeliveryNoteDetailsDialog .category-title {
  flex-wrap: wrap;
}

  .s-DeliveryNoteDetailsDialog .category-title a {
    color: #d99000;
  }

  .s-DeliveryNoteDetailsDialog .category-title::before {
    display:;
  }

  .s-DeliveryNoteDetailsDialog .category-title::after {
    margin-top: 2px;
    margin-bottom: 2px;
    flex-shrink: 0;
    display: inline-block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

.s-DeliveryNoteDetailsDialog .field .caption {
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}

.s-DeliveryNoteDetailsDialog {
      .UnitPrice input,
      .UnitAmount input,
      .PerUnitIGSTAmount input,
      .IGSTAmount input,
      .PerUnitCGSTAmount input,
      .CGSTAmount input, 
      .PerUnitSGSTAmount input,
      .SGSTAmount input,
      .PerUnitPrice input,
      .NetAmount input

{
  text-align: right;
}

.UnitAmount input,
.PerUnitPrice input,
.NetAmount input {
  font-weight: bold;
}

}

.s-DeliveryNotesGrid .slick-header-columns {
  white-space: normal;
  overflow: hidden;
  height: auto !important;
  align-items: center; /* Vertically centers the text */
}

.s-DeliveryNotesGrid .slick-header-column {
  overflow: visible;
  height: auto;
}

.s-DeliveryNotesGrid .slick-header {
  padding: 0;
  background-color: aliceblue;
}

.s-DeliveryNotesGrid .slick-column-name {
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  line-height: 20px;
  max-height: 40px; /* Maximum height for two lines */
  min-height: 30px; /* Minimum height for a single line */
  word-wrap: break-word;
}

/*To set the panel width to 900px*/  
@media (min-width: 992px) 
{
  .s-DeliveryNoteDetailsDialog .modal 
  {
    --bs-modal-width: 900px;
    position: absolute;
    cursor: move;
  }

  .s-DeliveryNoteDetailsDialog .modal-dialog 
  {
    max-width: 900px;
    margin: auto;
  }
}
/*END To set the panel width to 900px*/  
/*---------------*/

/*----- ProformaInvoicesDialog Category formatting ----- */
.s-ProformaInvoicesDialog .category-title {
  flex-wrap: wrap;
}

  .s-ProformaInvoicesDialog .category-title a {
    color: #d99000;
  }

  .s-ProformaInvoicesDialog .category-title::before {
    display:;
  }

  .s-ProformaInvoicesDialog .category-title::after {
    margin-top: 2px;
    margin-bottom: 2px;
    flex-shrink: 0;
    display: inline-block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

.s-ProformaInvoicesDialog .field .caption {
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}

.field.ProformaInvoiceDetailsList label.caption {
  display: none;
}

/*This will set the QuotationDetailsGrid Height to Minimum and Max height of the grid.*/
.s-ProformaInvoicesDialog .s-DataGrid {
  min-height: 299px;
  max-height: none;
}

.s-ProformaInvoicesDialog {
  .ProformaInvoiceNo input, .GrandTotal input

{
  font-weight: bold;
}

.RoundingOff input,
.GrandTotal input,
.ProformaInvoiceAmt input {
  text-align: right;
}

}

.s-ProformaInvoicesDialog .s-DataGrid {
  padding: 5px;
}

.s-ProformaInvoicesDialog .slick-header-columns {
  white-space: break-spaces;
  word-wrap: break-word;
  overflow: visible;
  min-height: 46px;
  height: auto !important;
}

.s-ProformaInvoicesDialog .slick-header {
  padding: 0;
  background-color: aliceblue;
}

.s-ProformaInvoicesDialog .slick-column-name {
  line-height: 20px;
}

/*----- ProformaInvoiceDetailsDialong Category formatting ----- */
.s-ProformaInvoiceDetailsDialog .category-title {
  flex-wrap: wrap;
}

  .s-ProformaInvoiceDetailsDialog .category-title a {
    color: #d99000;
  }

  .s-ProformaInvoiceDetailsDialog .category-title::before {
    display:;
  }

  .s-ProformaInvoiceDetailsDialog .category-title::after {
    margin-top: 2px;
    margin-bottom: 2px;
    flex-shrink: 0;
    display: inline-block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

.s-ProformaInvoiceDetailsDialog .field .caption {
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}

.s-ProformaInvoiceDetailsDialog 
{
    .UnitPrice input, 
    .UnitAmount input, 
    .DiscountAmountPerUnit input, 
    .NetDiscountAmount input, 
    .TaxableAmountPerUnit input, 
    .NetTaxableAmount input, 
    .IGSTAmountPerUnit input, 
    .NetIGSTAmount input, 
    .CGSTAmountPerUnit input, 
    .NetCGSTAmount input, 
    .SGSTAmountPerUnit input, 
    .NetSGSTAmount input, 
    .NetPricePerUnit input, 
    .NetAmount input

      {
        text-align: right;
      }

   .Quantity input,
   .UnitPrice input,
   .OrderUnitAmount input,
   .PricePerUnit input,
   .NetTaxableAmount input,
   .NetPricePerUnit input,
   .NetAmount input

     {
       font-weight: bold;
     }
}

.s-ProformaInvoicesGrid .slick-header-columns {
  white-space: normal;
  overflow: hidden;
  height: auto !important;
  align-items: center; /* Vertically centers the text */
}

.s-ProformaInvoicesGrid .slick-header-column {
  overflow: visible;
  height: auto;
}

.s-ProformaInvoicesGrid .slick-header {
  padding: 0;
  background-color: aliceblue;
}

.s-ProformaInvoicesGrid .slick-column-name {
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  line-height: 20px;
  max-height: 40px; /* Maximum height for two lines */
  min-height: 30px; /* Minimum height for a single line */
  word-wrap: break-word;
}

/*To set the panel width to 900px*/  
@media (min-width: 992px) 
{
  .s-ProformaInvoiceDetailsDialog .modal 
    {
      --bs-modal-width: 900px;
      position: absolute;
      cursor: move;
    }

  .s-ProformaInvoiceDetailsDialog .modal-dialog 
    {
      max-width: 900px;
      margin: auto;
    }
}
/*END To set the panel width to 900px*/  
/*---------------*/

/*----- InvoicesDialog Category formatting ----- */
.s-InvoicesDialog .category-title 
  {
    flex-wrap: wrap;
  }

  .s-InvoicesDialog .category-title a 
  {
    color: #d99000;
  }

  .s-InvoicesDialog .category-title::before 
  {
    display:;
  }

  .s-InvoicesDialog .category-title::after 
  {
    margin-top: 2px;
    margin-bottom: 2px;
    flex-shrink: 0;
    display: inline-block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

.s-InvoicesDialog .field .caption 
  {
    display: flex;
    flex-direction: row-reverse;
    justify-content: start;
  }

.field.InvoiceDetailsList label.caption 
  {
    display: none;
  }

/*This will set the DetailsGrid Height to Minimum and Max height of the grid.*/
.s-InvoicesDialog .s-DataGrid 
  {
    min-height: 299px;
    max-height: none;
  }

.s-InvoicesDialog 
{
    .InvoiceAmount input, 
    .RoundingOff input, 
    .GrandTotal input
      {
        text-align: right;
      }

   .InvoiceNo input,
   .GrandTotal input 
      {
        font-weight: bold;
      }
 
    .GrandTotal label 
    {
        font-weight: bold;
    }
}

}

.s-InvoicesDialog .s-DataGrid {
  padding: 5px;
}

.s-InvoicesDialog .slick-header-columns {
  white-space: break-spaces;
  word-wrap: break-word;
  overflow: visible;
  min-height: 46px;
  height: auto !important;
}

.s-InvoicesDialog .slick-header {
  padding: 0;
  background-color: aliceblue;
}

.s-InvoicesDialog .slick-column-name {
  line-height: 20px;
}

.cancelled-invoice-row {
  background-color: #fff0f0 !important; /* background color for Cancelled Invoice */
}


/*----- InvoiceDetailsDialong Category formatting ----- */
.s-InvoiceDetailsDialog .category-title {
  flex-wrap: wrap;
}

  .s-InvoiceDetailsDialog .category-title a {
    color: #d99000;
  }

  .s-InvoiceDetailsDialog .category-title::before {
    display:;
  }

  .s-InvoiceDetailsDialog .category-title::after {
    margin-top: 2px;
    margin-bottom: 2px;
    flex-shrink: 0;
    display: inline-block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

.s-InvoiceDetailsDialog .field .caption {
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}

.s-InvoiceDetailsDialog 
{
      .UnitPrice input, 
      .NetUnitAmount input, 
      .DiscountAmountPerUnit input, 
      .NetDiscountAmount input, 
      .TaxableAmountPerUnit input, 
      .NetTaxableAmount input, 
      .IGSTAmountPerUnit input, 
      .NetIGSTAmount input, 
      .CGSTAmountPerUnit input, 
      .NetCGSTAmount input, 
      .SGSTAmountPerUnit input, 
      .NetSGSTAmount input, 
      .NetPricePerUnit input, 
      .NetAmount input
      {
        text-align: right;
      }

      .Quantity input,
      .UnitPrice input,
      .OrderUnitAmount input,
      .PricePerUnit input,
      .NetTaxableAmount input,
      .NetPricePerUnit input,
      .NetAmount input 
      {
        font-weight: bold;
      }
}

.s-InvoicesGrid .slick-header-columns {
  white-space: normal;
  overflow: hidden;
  height: auto !important;
  align-items: center; /* Vertically centers the text */
}

.s-InvoicesGrid .slick-header-column {
  overflow: visible;
  height: auto;
}

.s-InvoicesGrid .slick-header {
  padding: 0;
  background-color: aliceblue;
}

.s-InvoicesGrid .slick-column-name {
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  line-height: 20px;
  max-height: 40px; /* Maximum height for two lines */
  min-height: 30px; /* Minimum height for a single line */
  word-wrap: break-word;
}

/*To set the panel width to 900px*/  
@media (min-width: 992px) 
{
  .s-InvoiceDetailsDialog .modal {
    --bs-modal-width: 900px;
    position: absolute;
    cursor: move;
  }

  .s-InvoiceDetailsDialog .modal-dialog {
    max-width: 900px;
    margin: auto;
  }
}
/*END To set the panel width to 900px*/  
/*---------------*/

/*----- ReceiptsDialog Category formatting ----- */
.s-ReceiptsDialog .category-title 
  {
      flex-wrap: wrap;
  }

  .s-ReceiptsDialog .category-title a 
  {
    color: #d99000;
  }

  .s-ReceiptsDialog .category-title::before 
  {
    display:;
  }

  .s-ReceiptsDialog .category-title::after 
  {
    margin-top: 2px;
    margin-bottom: 2px;
    flex-shrink: 0;
    display: inline-block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

.s-ReceiptsDialog .field .caption 
{
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}

.field.ReceiptDetailsList label.caption {
  display: none;
}

/*This will set the QuotationDetailsGrid Height to Minimum and Max height of the grid.*/
.s-ReceiptsDialog .s-DataGrid 
{
  min-height: 299px;
  max-height: none;
}

.s-ReceiptsDialog 
{
      .TotalReceivable input, 
      .OnAccount input, 
      .AmountReceived input
      {
        text-align: right;
      }

      .TotalReceivable input,
      .AmountReceived input,
      .ReceiptNo input
      {
        font-weight: bold;
      }
      }

.s-ReceiptsDialog .grid-toolbar:empty,
.s-ReceiptsDialog .grid-toolbar:has(.tool-group:empty) {
  display: none !important;
}

.s-ReceiptsDialog .s-DataGrid {
  padding: 5px;
}

.s-ReceiptsDialog .slick-header-columns {
  white-space: break-spaces;
  word-wrap: break-word;
  overflow: visible;
  min-height: 46px;
  height: auto !important;
}

.s-ReceiptsDialog .slick-header {
  padding: 0;
  background-color: aliceblue;
}

.s-ReceiptsDialog .slick-column-name {
  line-height: 20px;
}

/*----- Receipt Details Dialog Category formatting ----- */
.s-ReceiptDetailsDialog .category-title {
  flex-wrap: wrap;
}

  .s-ReceiptDetailsDialog .category-title a {
    color: #d99000;
  }

  .s-ReceiptDetailsDialog .category-title::before {
    display: ;
  }

  .s-ReceiptDetailsDialog .category-title::after {
    margin-top: 0px;
    margin-bottom: 0px;
    padding-top: 0px;
    flex-shrink: 0;
    display: inline-block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

.s-ReceiptDetailsDialog .field .caption {
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}

.s-ReceiptsGrid .slick-header-columns {
  white-space: normal;
  overflow: hidden;
  height: auto !important;
  align-items: center; /* Vertically centers the text */
}

.s-ReceiptsGrid .slick-header-column {
  overflow: visible;
  height: auto;
}

.s-ReceiptsGrid .slick-header {
  padding: 0;
  background-color: aliceblue;
}

.s-ReceiptsGrid .slick-column-name {
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  line-height: 20px;
  max-height: 40px; /* Maximum height for two lines */
  min-height: 30px; /* Minimum height for a single line */
  word-wrap: break-word;
}
/*----- Sales Returns Dialog Category formatting ----- */
.s-SalesReturnsDialog .category-title {
  flex-wrap: wrap;
}

  .s-SalesReturnsDialog .category-title a {
    color: #d99000;
  }

  .s-SalesReturnsDialog .category-title::before {
    display:;
  }

  .s-SalesReturnsDialog .category-title::after {
    margin-top: 2px;
    margin-bottom: 2px;
    flex-shrink: 0;
    display: inline-block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

.s-SalesReturnsDialog .field .caption {
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}


.field.SalesReturnDetailsList label.caption {
  display: none;
}

/*This will set the QuotationDetailsGrid Height to Minimum and Max height of the grid.*/
.s-SalesReturnsDialog .s-DataGrid {
  min-height: 299px;
  max-height: none;
}

.s-SalesReturnsDialog {
  .SalesReturnNo input

{
  font-weight: bold;
}

}

.s-SalesReturnsDialog .s-DataGrid {
  padding: 5px;
}

.s-SalesReturnsDialog .slick-header-columns {
  white-space: break-spaces;
  word-wrap: break-word;
  overflow: visible;
  min-height: 33px;
  height: auto !important;
}

.s-SalesReturnsDialog .slick-header {
  padding: 0;
  background-color: aliceblue;
}

.s-SalesReturnsDialog .slick-column-name {
  line-height: 20px;
}

/*----- Sales Return Details Category formatting ----- */
.s-SalesReturnDetailsDialog .category-title {
  flex-wrap: wrap;
}

  .s-SalesReturnDetailsDialog .category-title a {
    color: #d99000;
  }

  .s-SalesReturnDetailsDialog .category-title::before {
    display:;
  }

  .s-SalesReturnDetailsDialog .category-title::after {
    margin-top: 2px;
    margin-bottom: 2px;
    flex-shrink: 0;
    display: inline-block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

.s-SalesReturnDetailsDialog .field .caption {
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}

.s-SalesReturnDetailsDialog .caption {
  width: 160px;
  text-align: left;
}

.s-SalesReturnDetailsDialog 
{
    .UnitPrice input, 
    .UnitAmount input, 
    .DiscountAmountPerUnit input, 
    .NetDiscountAmount input, 
    .TaxableAmountPerUnit input, 
    .NetTaxableAmount input, 
    .IGSTAmountPerUnit input, 
    .NetIGSTAmount input, 
    .CGSTAmountPerUnit input, 
    .NetCGSTAmount input, 
    .SGSTAmountPerUnit input, 
    .NetSGSTAmount input, 
    .NetPricePerUnit input, 
    .NetAmount input
    {
      text-align: right;
    }

    .Quantity input,
    .UnitPrice input,
    .OrderUnitAmount input,
    .PricePerUnit input,
    .NetTaxableAmount input,
    .NetPricePerUnit input,
    .NetAmount input 
    {
      font-weight: bold;
    }

}

.s-SalesReturnsGrid .slick-header-columns {
  white-space: normal;
  overflow: hidden;
  height: auto !important;
  align-items: center; /* Vertically centers the text */
}

.s-SalesReturnsGrid .slick-header-column {
  overflow: visible;
  height: auto;
}

.s-SalesReturnsGrid .slick-header {
  padding: 0;
  background-color: aliceblue;
}

.s-SalesReturnsGrid .slick-column-name {
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  line-height: 20px;
  max-height: 40px; /* Maximum height for two lines */
  min-height: 30px; /* Minimum height for a single line */
  word-wrap: break-word;
}
/*----- CreditNotesDialog Category formatting ----- */
.s-CreditNotesDialog .category-title {
  flex-wrap: wrap;
}

  .s-CreditNotesDialog .category-title a {
    color: #d99000;
  }

  .s-CreditNotesDialog .category-title::before {
    display:;
  }

  .s-CreditNotesDialog .category-title::after {
    margin-top: 2px;
    margin-bottom: 2px;
    flex-shrink: 0;
    display: inline-block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

.s-CreditNotesDialog .field .caption {
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}


.field.CreditNoteDetailsList label.caption {
  display: none;
}

/*This will set the DetailsGrid Height to Minimum and Max height of the grid.*/
.s-CreditNotesDialog .s-DataGrid {
  min-height: 299px;
  max-height: none;
}

.s-CreditNotesDialog {
  .CreditNoteNo input, .CreditNoteAmount input

{
  font-weight: bold;
}

.CreditNoteAmount input {
  text-align: right;
}

}

.s-CreditNotesDialog .s-DataGrid {
  padding: 5px;
}

.s-CreditNotesDialog .slick-header-columns {
  white-space: break-spaces;
  word-wrap: break-word;
  overflow: visible;
  min-height: 46px;
  height: auto !important;
}

.s-CreditNotesDialog .slick-header {
  padding: 0;
  background-color: aliceblue;
}

.s-CreditNotesDialog .slick-column-name {
  line-height: 20px;
}

/*----- CreditNoteDetailsDialog Category formatting ----- */
.s-CreditNoteDetailsDialog .category-title {
  flex-wrap: wrap;
}

  .s-CreditNoteDetailsDialog .category-title a {
    color: #d99000;
  }

  .s-CreditNoteDetailsDialog .category-title::before {
    display:;
  }

  .s-CreditNoteDetailsDialog .category-title::after {
    margin-top: 2px;
    margin-bottom: 2px;
    flex-shrink: 0;
    display: inline-block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

.s-CreditNoteDetailsDialog .field .caption {
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}
.s-CreditNoteDetailsDialog {
      .DiscountAmountPerUnit input,
      .NetDiscountAmount input,
      .UnitAmount input,
      .UnitPrice input,
      .TaxableAmountPerUnit input,
      .NetTaxableAmount input, 
      .IGSTAmountPerUnit input, 
      .NetIGSTAmount input, 
      .CGSTAmountPerUnit input, 
      .NetCGSTAmount input,
      .SGSTAmountPerUnit input,
      .NetSGSTAmount input,
      .NetPricePerUnit input,
      .NetAmount input
         
    {
     text-align: right;
    }
    .NetPricePerUnit input,
    .NetAmount input,
    .NetTaxableAmount input,
    .UnitPrice input {
      font-weight: bold;
    }
}

.s-CreditNotesGrid .slick-header-columns {
  white-space: normal;
  overflow: hidden;
  height: auto !important;
  align-items: center; /* Vertically centers the text */
}

.s-CreditNotesGrid .slick-header-column {
  overflow: visible;
  height: auto;
}

.s-CreditNotesGrid .slick-header {
  padding: 0;
  background-color: aliceblue;
}

.s-CreditNotesGrid .slick-column-name {
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  line-height: 20px;
  max-height: 40px; /* Maximum height for two lines */
  min-height: 30px; /* Minimum height for a single line */
  word-wrap: break-word;
}

/*To set the panel width to 900px*/  
@media (min-width: 992px) 
{
  .s-CreditNoteDetailsDialog .modal 
  {
    --bs-modal-width: 900px;
    position: absolute;
    cursor: move;
  }

  .s-CreditNoteDetailsDialog .modal-dialog 
  {
    max-width: 900px;
    margin: auto;
  }
}

/*END To set the panel width to 900px*/  
/*---------------*/

/*-------------- Purchase Module -------------------*/
/*----- VendorsDialog Category formatting ----- */
.s-VendorsDialog .category-title {
  flex-wrap: wrap;
}

  .s-VendorsDialog .category-title a {
    color: #d99000;
  }

  .s-VendorsDialog .category-title::before {
    display:;
  }

  .s-VendorsDialog .category-title::after {
    margin-top: 2px;
    margin-bottom: 2px;
    flex-shrink: 0;
    display: inline-block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

.s-VendorsDialog .field .caption {
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}

.s-VendorsDialog {
  .VendorName input

{
  font-weight: bold;
}

}

/*----- VendorContactsDialong Category formatting ----- */
.s-VendorContactDialog .category-title {
  flex-wrap: wrap;
}

  .s-VendorContactDialog .category-title a {
    color: #d99000;
  }

  .s-VendorContactDialog .category-title::before {
    display:;
  }

  .s-VendorContactDialog .category-title::after {
    margin-top: 2px;
    margin-bottom: 2px;
    flex-shrink: 0;
    display: inline-block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

.field.VendorContactsList label.caption {
  display: none;
}

.s-VendorContactDialog .field .caption {
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}

.s-VendorContactsDialog .size {
  width: 750px;
  padding: 2px;
}

.s-VendorsGrid .slick-header-columns {
  white-space: normal;
  overflow: hidden;
  height: auto !important;
  align-items: center; /* Vertically centers the text */
}

.s-VendorsGrid .slick-header-column {
  overflow: visible;
  height: auto;
}

.s-VendorsGrid .slick-header {
  padding: 0;
  background-color: aliceblue;
}

.s-VendorsGrid .slick-column-name {
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  line-height: 20px;
  max-height: 40px; /* Maximum height for two lines */
  min-height: 30px; /* Minimum height for a single line */
  word-wrap: break-word;
}
/*----- Purchase Orders Dialog Category formatting ----- */
.s-PurchaseOrdersDialog .category-title {
  flex-wrap: wrap;
}

  .s-PurchaseOrdersDialog .category-title a {
    color: #d99000;
  }

  .s-PurchaseOrdersDialog .category-title::before {
    display:;
  }

  .s-PurchaseOrdersDialog .category-title::after {
    margin-top: 2px;
    margin-bottom: 2px;
    flex-shrink: 0;
    display: inline-block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

.s-PurchaseOrdersDialog .field .caption {
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}

.field.PurchaseOrderDetailsList label.caption {
  display: none;
}

/*This will set the Grid Height to Minimum and Max height of the grid.*/
.s-PurchaseOrdersDialog .s-DataGrid {
  min-height: 299px;
  max-height: none;
}

.s-PurchaseOrdersDialog {
  .PurchaseOrderNo input

{
  font-weight: bold;
}

}
.s-PurchaseOrdersDialog .caption,
{
  width: 150px;
  text-align: left;
}

.s-PurchaseOrdersDialog 
  {
      .POAmount input, 
      .RoundingOff input, 
      .GrandTotal input, 
      .TDSAmount input, 
      .TCSAmount input
        {
          text-align: right;
        }

      .DeliveryDueDate input,
      .PaymentDueDate input,
      .PoAmount input,
      .GrandTotal input
        {
          font-weight: bold;
        }
  }

.s-PurchaseOrdersDialog .s-DataGrid {
  padding: 5px;
}

.s-PurchaseOrdersDialog .slick-header-columns {
  white-space: break-spaces;
  word-wrap: break-word;
  overflow: visible;
  min-height: 46px;
  height: auto !important;
}

.s-PurchaseOrdersDialog .slick-header {
  padding: 0;
  background-color: aliceblue;
}

.s-PurchaseOrdersDialog .slick-column-name {
  line-height: 20px;
}

.s-PurchaseOrdersGrid .slick-header-columns {
  white-space: normal;
  overflow: hidden;
  height: auto !important;
  align-items: center; /* Vertically centers the text */
}

.s-PurchaseOrdersGrid .slick-header-column {
  overflow: visible;
  height: auto;
}

.s-PurchaseOrdersGrid .slick-header {
  padding: 0;
  background-color: aliceblue;
}

.s-PurchaseOrdersGrid .slick-column-name {
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  line-height: 20px;
  max-height: 40px; /* Maximum height for two lines */
  min-height: 30px; /* Minimum height for a single line */
  word-wrap: break-word;
}
/*----- Purchase Order Details Dialong Category formatting ----- */
.s-PurchaseOrderDetailsDialog .category-title {
  flex-wrap: wrap;
}

  .s-PurchaseOrderDetailsDialog .category-title a {
    color: #d99000;
  }

  .s-PurchaseOrderDetailsDialog .category-title::before {
    display:;
  }

  .s-PurchaseOrderDetailsDialog .category-title::after {
    margin-top: 2px;
    margin-bottom: 2px;
    flex-shrink: 0;
    display: inline-block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

.s-PurchaseOrderDetailsDialog .field .caption {
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}

.s-PurchaseOrderDetailsDialog 
{
    .UnitPrice input, 
    .UnitAmount input, 
    .DiscountAmountPerUnit input, 
    .NetDiscountAmount input, 
    .TaxableAmountPerUnit input, 
    .NetTaxableAmount input, 
    .IGSTAmountPerUnit input, 
    .NetIGSTAmount input, 
    .CGSTAmountPerUnit input, 
    .NetCGSTAmount input, 
    .SGSTAmountPerUnit input, 
    .NetSGSTAmount input, 
    .NetPricePerUnit input, 
    .NetAmount input
    {
      text-align: right;
    }

    .Quantity input,
    .UnitPrice input,
    .OrderUnitAmount input,
    .PricePerUnit input,
    .NetTaxableAmount input,
    .NetPricePerUnit input,
    .NetAmount input 
    {
      font-weight: bold;
    }

}

/*To set the panel width to 900px*/  
@media (min-width: 992px) 
{
  .s-PurchaseOrderDetailsDialog .modal {
    --bs-modal-width: 900px;
    position: absolute;
    cursor: move;
  }

  .s-PurchaseOrderDetailsDialog .modal-dialog {
    max-width: 900px;
    margin: auto;
  }
}
/*END To set the panel width to 900px*/  
/*---------------*/

/*----- PO Amendments Category formatting ----- */
.s-PoAmendmentsDialog .category-title {
  flex-wrap: wrap;
}

  .s-PoAmendmentsDialog .category-title a {
    color: #d99000;
  }

  .s-PoAmendmentsDialog .category-title::before {
    display:;
  }

  .s-PoAmendmentsDialog .category-title::after {
    margin-top: 2px;
    margin-bottom: 2px;
    flex-shrink: 0;
    display: inline-block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

.s-PoAmendmentsDialog .field .caption {
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}

.field.PoAmendmentDetailsList label.caption {
  display: none;
}

/*This will set the QuotationDetailsGrid Height to Minimum and Max height of the grid.*/
.s-PoAmendmentsDialog .s-DataGrid {
  min-height: 299px;
  max-height: none;
}

.s-PoAmendmentsDialog {
      .RoundingOff input, 
      .GrandTotal input, 
      .PoAmendmentAmount input
    {
      text-align: right;
    }

.GrandTotal input,
.POAmendmentNo input
{
  font-weight: bold;
}

}

.s-PoAmendmentsDialog .s-DataGrid {
  padding: 5px;
}

.s-PoAmendmentsDialog .slick-header-columns {
  white-space: break-spaces;
  word-wrap: break-word;
  overflow: visible;
  min-height: 46px;
  height: auto !important;
}

.s-PoAmendmentsDialog .slick-header {
  padding: 0;
  background-color: aliceblue;
}

.s-PoAmendmentsDialog .slick-column-name {
  line-height: 20px;
}

/*----- PO Amendments Details Dialog Category formatting ----- */
.s-PoAmendmentDetailsDialog .category-title {
  flex-wrap: wrap;
}

  .s-PoAmendmentDetailsDialog .category-title a {
    color: #d99000;
  }

  .s-PoAmendmentDetailsDialog .category-title::before {
    display:;
  }

  .s-PoAmendmentDetailsDialog .category-title::after {
    margin-top: 2px;
    margin-bottom: 2px;
    flex-shrink: 0;
    display: inline-block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

.s-PoAmendmentDetailsDialog .field .caption {
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}

.s-PoAmendmentsGrid .slick-header-columns {
  white-space: normal;
  overflow: hidden;
  height: auto !important;
  align-items: center; /* Vertically centers the text */
}

.s-PoAmendmentsGrid .slick-header-column {
  overflow: visible;
  height: auto;
}

.s-PoAmendmentsGrid .slick-header {
  padding: 0;
  background-color: aliceblue;
}

.s-PoAmendmentsGrid .slick-column-name {
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  line-height: 20px;
  max-height: 40px; /* Maximum height for two lines */
  min-height: 30px; /* Minimum height for a single line */
  word-wrap: break-word;
}

.s-PoAmendmentDetailsDialog {
      .POUnitPrice input,
      .POUnitAmount input,
      .AmendedUnitPrice input,
      .AmendedUnitAmount input, 
      .TaxableAmountPerUnit input,
      .NetTaxableAmount input,
      .IGSTAmountPerUnit input, 
      .NetIGSTAmount input, 
      .CGSTAmountPerUnit input,
      .NetCGSTAmount input, 
      .SGSTAmountPerUnit input,
      .NetSGSTAmount input, 
      .NetAmount input,
      .NetPricePerUnit input
      {
        text-align: right;
      }

      .NetPricePerUnit input,
      .NetAmount input,
      .NetTaxableAmount input,
      .AmendedUnitAmount input,
      .POUnitPrice input {
        font-weight: bold;
      }
}
/*To set the panel width to 900px*/
@media (min-width: 992px) {
  .s-PoAmendmentDetailsDialog .modal {
    --bs-modal-width: 900px;
    position: absolute;
    cursor: move;
  }

  .s-PoAmendmentDetailsDialog .modal-dialog {
    max-width: 900px;
    margin: auto;
  }
}
/*END To set the panel width to 900px*/  
/*---------------*/

/*----- GoodsReceivedNotes Dialog Category formatting ----- */
.s-GoodsReceivedNotesDialog .category-title 
    {
      flex-wrap: wrap;
    }

.s-GoodsReceivedNotesDialog .category-title a 
    {
      color: #d99000;
    }

.s-GoodsReceivedNotesDialog .category-title::before 
    {
      display:;
    }

.s-GoodsReceivedNotesDialog .category-title::after 
    {
      margin-top: 2px;
      margin-bottom: 2px;
      flex-shrink: 0;
      display: inline-block;
      width: 100%;
      border-bottom: 1px solid #7380C4;
    }

.s-GoodsReceivedNotesDialog .field .caption 
    {
      display: flex;
      flex-direction: row-reverse;
      justify-content: start;
    }

.field.GoodsReceivedNoteDetailsList label.caption 
    {
      display: none;
    }

.s-GoodsReceivedNotesDialog .s-DataGrid 
    {
      min-height: 299px;
      max-height: none;
    }

.s-GoodsReceivedNotesDialog 
    {
      .GoodsReceivedNoteNo input

        {
          font-weight: bold;
        }
    }

.s-GoodsReceivedNotesDialog .s-DataGrid {
  padding: 5px;
}

.s-GoodsReceivedNotesDialog .slick-header-columns {
  white-space: break-spaces;
  word-wrap: break-word;
  overflow: visible;
  /*min-height: 46px;*/
  height: auto !important;
}

.s-GoodsReceivedNotesDialog .slick-header {
  padding: 0;
  background-color: aliceblue;
}

.s-GoodsReceivedNotesDialog .slick-column-name {
  line-height: 20px;
}
/*---------- */


/*----- GoodsReceivedNote Details Dialong Category formatting ----- */
.s-GoodsReceivedNoteDetailsDialog .size 
{
  width: 950px;
}

.s-GoodsReceivedNoteDetailsDialog .category-title 
    {
      flex-wrap: wrap;
    }

.s-GoodsReceivedNoteDetailsDialog .category-title a 
    {
      color: #d99000;
    }

.s-GoodsReceivedNoteDetailsDialog .category-title::before 
    {
      display:;
    }

.s-GoodsReceivedNoteDetailsDialog .category-title::after 
    {
      margin-top: 2px;
      margin-bottom: 2px;
      flex-shrink: 0;
      display: inline-block;
      width: 100%;
      border-bottom: 1px solid #7380C4;
    }

.s-GoodsReceivedNoteDetailsDialog .field .caption 
    {
      display: flex;
      flex-direction: row-reverse;
      justify-content: start;
    }

.s-GoodsReceivedNoteDetailsDialog 
    {
      .ReceivedQuantity input,
      .ReceivedUnit input 
        {
          font-weight: bold;
        }
    }

.s-GoodsReceivedNotesGrid .slick-header-columns {
  white-space: normal;
  overflow: hidden;
  height: auto !important;
  align-items: center; /* Vertically centers the text */
}

.s-GoodsReceivedNotesGrid .slick-header-column {
  overflow: visible;
  height: auto;
}

.s-GoodsReceivedNotesGrid .slick-header {
  padding: 0;
  background-color: aliceblue;
}

.s-GoodsReceivedNotesGrid .slick-column-name {
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  line-height: 20px;
  max-height: 40px; /* Maximum height for two lines */
  min-height: 30px; /* Minimum height for a single line */
  word-wrap: break-word;
}
@media (min-width: 992px) 
    {
      .s-GoodsReceivedNoteDetailsDialog .modal 
      {
        --bs-modal-width: 900px;
        position: absolute;
        cursor: move;
      }

      .s-GoodsReceivedNoteDetailsDialog .modal-dialog 
      {
        max-width: 900px;
        margin: auto;
      }
    }
/*--------------*/

/*----- VendorBills Dialog Category formatting ----- */
.s-VendorBillsDialog .category-title 
    {
      flex-wrap: wrap;
    }

.s-VendorBillsDialog .category-title a 
    {
      color: #d99000;
    }

.s-VendorBillsDialog .category-title::before 
    {
      display:;
    }

.s-VendorBillsDialog .category-title::after 
    {
      margin-top: 2px;
      margin-bottom: 2px;
      flex-shrink: 0;
      display: inline-block;
      width: 100%;
      border-bottom: 1px solid #7380C4;
    }

.s-VendorBillsDialog .field .caption 
    {
      display: flex;
      flex-direction: row-reverse;
      justify-content: start;
    }

.field.VendorBillDetailsList label.caption 
    {
      display: none;
    }

.s-VendorBillsDialog .s-DataGrid 
    {
      min-height: 299px;
      max-height: none;
    }

.s-VendorBillsDialog 
    {
      .VendorBillNo input,
      .VendorBillDate input,
      .GrandTotal input,
      .VendorBillAmount input

        {
          font-weight: bold;
        }
      
      .TDSAmount input,
      .TCSAmount input,
      .RoundingOff input,
      .GrandTotal input,
      .VendorBillAmount input

       {
         text-align: right;
       }
    }

.s-VendorBillsDialog .s-DataGrid {
  padding: 5px;
}

.s-VendorBillsDialog .slick-header-columns {
  white-space: break-spaces;
  word-wrap: break-word;
  overflow: visible;
  min-height: 46px;
  height: auto !important;
}

.s-VendorBillsDialog .slick-header {
  padding: 0;
  background-color: aliceblue;
}

.s-VendorBillsDialog .slick-column-name {
  line-height: 20px;
}
/*---------- */


/*----- Vendor Bill Details Dialong Category formatting ----- */
.s-VendorBillDetailsDialog .size 
{
  width: 950px;
}

.s-VendorBillDetailsDialog .category-title 
    {
      flex-wrap: wrap;
    }

.s-VendorBillDetailsDialog .category-title a 
    {
      color: #d99000;
    }

.s-VendorBillDetailsDialog .category-title::before 
    {
      display:;
    }

.s-VendorBillDetailsDialog .category-title::after 
    {
      margin-top: 2px;
      margin-bottom: 2px;
      flex-shrink: 0;
      display: inline-block;
      width: 100%;
      border-bottom: 1px solid #7380C4;
    }

.s-VendorBillDetailsDialog .field .caption 
    {
      display: flex;
      flex-direction: row-reverse;
      justify-content: start;
    }

.s-VendorBillDetailsDialog 
    {
        .UnitPrice input, 
        .UnitAmount input, 
        .DiscountAmountPerUnit input, 
        .DiscountAmount input, 
        .TaxableAmountPerUnit input, 
        .NetTaxableAmount input, 
        .IGSTAmountPerUnit input, 
        .NetIGSTAmount input, 
        .CGSTAmountPerUnit input, 
        .NetCGSTAmount input, 
        .SGSTAmountPerUnit input, 
        .NetSGSTAmount input, 
        .NetPricePerUnit input, 
        .NetAmount input
        {
          text-align: right;
        }
      
        .Quantity input,
        .UnitPrice input,
        .OrderUnitAmount input,
        .PricePerUnit input,
        .NetTaxableAmount input,
        .NetPricePerUnit input,
        .NetAmount input 
        {
          font-weight: bold;
        }
    }

.s-VendorBillsGrid .slick-header-columns {
  white-space: normal;
  overflow: hidden;
  height: auto !important;
  align-items: center; /* Vertically centers the text */
}

.s-VendorBillsGrid .slick-header-column {
  overflow: visible;
  height: auto;
}

.s-VendorBillsGrid .slick-header {
  padding: 0;
  background-color: aliceblue;
}

.s-VendorBillsGrid .slick-column-name {
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  line-height: 20px;
  max-height: 40px; /* Maximum height for two lines */
  min-height: 30px; /* Minimum height for a single line */
  word-wrap: break-word;
}

@media (min-width: 992px) 
    {
      .s-VendorBillsDialog .modal 
      {
        --bs-modal-width: 900px;
        position: absolute;
        cursor: move;
      }

      .s-VendorBillDetailsDialog .modal-dialog 
      {
        max-width: 900px;
        margin: auto;
      }
    }
/*--------------*/


/*----- VendorPaymentsDialog Category formatting ----- */
.s-VendorPaymentsDialog .category-title {
  flex-wrap: wrap;
}

  .s-VendorPaymentsDialog .category-title a {
    color: #d99000;
  }

  .s-VendorPaymentsDialog .category-title::before {
    display:;
  }

  .s-VendorPaymentsDialog .category-title::after {
    margin-top: 2px;
    margin-bottom: 2px;
    flex-shrink: 0;
    display: inline-block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

.s-VendorPaymentsDialog .field .caption {
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}


.field.VendorPaymentDetailsList label.caption {
  display: none;
}

/*This will set the DetailsGrid Height to Minimum and Max height of the grid.*/
.s-VendorPaymentsDialog .s-DataGrid {
  min-height: 299px;
  max-height: none;
}

.s-VendorPaymentsDialog {
  .PaymentVoucherNo input

{
  font-weight: bold;
}

}

.s-VendorPaymentsDialog .s-DataGrid {
  padding: 5px;
}

.s-VendorPaymentsDialog .slick-header-columns {
  white-space: break-spaces;
  word-wrap: break-word;
  overflow: visible;
  /*min-height: 46px;*/
  height: auto !important;
}

.s-VendorPaymentsDialog .slick-header {
  padding: 0;
  background-color: aliceblue;
}

.s-VendorPaymentsDialog .slick-column-name {
  line-height: 20px;
}

/*----- s-VendorPaymentDetailsDialog Category formatting ----- */
.s-VendorPaymentDetailsDialog .category-title {
  flex-wrap: wrap;
}

  .s-VendorPaymentDetailsDialog.category-title a {
    color: #d99000;
  }

  .s-VendorPaymentDetailsDialog .category-title::before {
    display:;
  }

  .s-VendorPaymentDetailsDialog .category-title::after {
    margin-top: 2px;
    margin-bottom: 2px;
    flex-shrink: 0;
    display: inline-block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

.s-VendorPaymentDetailsDialog .field .caption {
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}

.s-VendorPaymentsGrid .slick-header-columns {
  white-space: normal;
  overflow: hidden;
  height: auto !important;
  align-items: center; /* Vertically centers the text */
}

.s-VendorPaymentsGrid .slick-header-column {
  overflow: visible;
  height: auto;
}

.s-VendorPaymentsGrid .slick-header {
  padding: 0;
  background-color: aliceblue;
}

.s-VendorPaymentsGrid .slick-column-name {
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  line-height: 20px;
  max-height: 40px; /* Maximum height for two lines */
  min-height: 30px; /* Minimum height for a single line */
  word-wrap: break-word;
}
/*To set the panel width to 900px*/  
@media (min-width: 992px) 
{
  .s-PoAmendmentDetailsDialog .modal {
    --bs-modal-width: 900px;
    position: absolute;
    cursor: move;
  }

  .s-PoAmendmentDetailsDialog .modal-dialog {
    max-width: 900px;
    margin: auto;
  }
}
/*END To set the panel width to 900px*/  
/*---------------*/

/*----- PurchaseReturnsDialog Category formatting ----- */
.s-PurchaseReturnsDialog .category-title {
  flex-wrap: wrap;
}

  .s-PurchaseReturnsDialog .category-title a {
    color: #d99000;
  }

  .s-PurchaseReturnsDialog .category-title::before {
    display:;
  }

  .s-PurchaseReturnsDialog .category-title::after {
    margin-top: 2px;
    margin-bottom: 2px;
    flex-shrink: 0;
    display: inline-block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

.s-PurchaseReturnsDialog .field .caption {
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}


.field.PurchaseReturnDetailsList label.caption {
  display: none;
}

/*This will set the DetailsGrid Height to Minimum and Max height of the grid.*/
.s-PurchaseReturnsDialog .s-DataGrid {
  min-height: 299px;
  max-height: none;
}

.s-PurchaseReturnsDialog {
  .PurchaseReturnNo input

{
  font-weight: bold;
}

}

.s-PurchaseReturnsDialog .s-DataGrid {
  padding: 5px;
}

.s-PurchaseReturnsDialog .slick-header-columns {
  white-space: break-spaces;
  word-wrap: break-word;
  overflow: visible;
  min-height: 46px;
  height: auto !important;
}

.s-PurchaseReturnsDialog .slick-header {
  padding: 0;
  background-color: aliceblue;
}

.s-PurchaseReturnsDialog .slick-column-name {
  line-height: 20px;
}

/*----- PurchaseReturnDetailsDialog Category formatting ----- */
.s-PurchaseReturnDetailsDialog .category-title {
  flex-wrap: wrap;
}

  .s-PurchaseReturnDetailsDialog .category-title a {
    color: #d99000;
  }

  .s-PurchaseReturnDetailsDialog .category-title::before {
    display:;
  }

  .s-PurchaseReturnDetailsDialog .category-title::after {
    margin-top: 2px;
    margin-bottom: 2px;
    flex-shrink: 0;
    display: inline-block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

.s-PurchaseReturnDetailsDialog .field .caption {
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}

.s-PurchaseReturnsGrid .slick-header-columns {
  white-space: normal;
  overflow: hidden;
  height: auto !important;
  align-items: center; /* Vertically centers the text */
}

.s-PurchaseReturnsGrid .slick-header-column {
  overflow: visible;
  height: auto;
}

.s-PurchaseReturnsGrid .slick-header {
  padding: 0;
  background-color: aliceblue;
}

.s-PurchaseReturnsGrid .slick-column-name {
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  line-height: 20px;
  max-height: 40px; /* Maximum height for two lines */
  min-height: 30px; /* Minimum height for a single line */
  word-wrap: break-word;
}


/*----- DebitNotesDialog Category formatting ----- */
.s-DebitNotesDialog .category-title {
  flex-wrap: wrap;
}

  .s-DebitNotesDialog .category-title a {
    color: #d99000;
  }

  .s-DebitNotesDialog .category-title::before {
    display:;
  }

  .s-DebitNotesDialog .category-title::after {
    margin-top: 2px;
    margin-bottom: 2px;
    flex-shrink: 0;
    display: inline-block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

.s-DebitNotesDialog .field .caption {
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}


.field.DebitNoteDetailsList label.caption {
  display: none;
}

/*This will set the DetailsGrid Height to Minimum and Max height of the grid.*/
.s-DebitNotesDialog .s-DataGrid {
  min-height: 299px;
  max-height: none;
}

.s-DebitNotesDialog {
  .DebitNoteNo input, .DebitNoteAmount input

{
  font-weight: bold;
}

.DebitNoteAmount input {
  text-align: right;
}

}

.s-DebitNotesDialog .s-DataGrid {
  padding: 5px;
}

.s-DebitNotesDialog .slick-header-columns {
  white-space: break-spaces;
  word-wrap: break-word;
  overflow: visible;
  min-height: 46px;
  height: auto !important;
}

.s-DebitNotesDialog .slick-header {
  padding: 0;
  background-color: aliceblue;
}

.s-DebitNotesDialog .slick-column-name {
  line-height: 20px;
}

/*----- DebitNoteDetailsDialog Category formatting ----- */
.s-DebitNoteDetailsDialog .category-title {
  flex-wrap: wrap;
}

  .s-DebitNoteDetailsDialog .category-title a {
    color: #d99000;
  }

  .s-DebitNoteDetailsDialog .category-title::before {
    display:;
  }

  .s-DebitNoteDetailsDialog .category-title::after {
    margin-top: 2px;
    margin-bottom: 2px;
    flex-shrink: 0;
    display: inline-block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

.s-DebitNoteDetailsDialog .field .caption {
  display: flex;
  flex-direction: row-reverse;
  justify-content: start;
}

.s-DebitNoteDetailsDialog {
      .UnitPrice input,
      .UnitAmount input,
      .IGSTAmountPerUnit input,
      .NetIGSTAmount input,
      .CGSTAmountPerUnit input,
      .TaxableAmountPerUnit input,
      .NetCGSTAmount input,
      .SGSTAmountPerUnit input,
      .NetSGSTAmount input,
      .NetPricePerUnit input,
      .NetAmount input
      {
        text-align: right;
      }
      .NetPricePerUnit input,
      .NetAmount input,
      .UnitPrice input
      {
        font-weight: bold;
      }
}

.s-DebitNotesGrid .slick-header-columns {
  white-space: normal;
  overflow: hidden;
  height: auto !important;
  align-items: center; /* Vertically centers the text */
}

.s-DebitNotesGrid .slick-header-column {
  overflow: visible;
  height: auto;
}

.s-DebitNotesGrid .slick-header {
  padding: 0;
  background-color: aliceblue;
}

.s-DebitNotesGrid .slick-column-name {
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  line-height: 20px;
  max-height: 40px; /* Maximum height for two lines */
  min-height: 30px; /* Minimum height for a single line */
  word-wrap: break-word;
}
    /*To set the panel width to 900px*/  
@media (min-width: 992px) 
{
  .s-DebitNoteDetailsDialog .modal 
  {
    --bs-modal-width: 900px;
    position: absolute;
    cursor: move;
  }

  .s-DebitNoteDetailsDialog .modal-dialog 
  {
    max-width: 900px;
    margin: auto;
  }
}
/*END To set the panel width to 900px*/  
/*---------------*/

/*----- EmployeesDialog Category formatting ----- */

  .s-EmployeesDialog .category-title {
    flex-wrap: wrap;
  }

  .s-EmployeesDialog .category-title a {
    color: #d99000;
  }

  .s-EmployeesDialog .category-title::after {
    margin-top: 2px;
    margin-bottom: 2px;
    flex-shrink: 0;
    display: inline-block;
    width: 100%;
    border-bottom: 1px solid #7380C4;
  }

  .s-EmployeesDialog .field .caption {
    display: flex;
    flex-direction: row-reverse;
    justify-content: start;
  }

  /*----- HsnSummaryGrid and SalesRegisterGrid  formatting ----- */

  .s-HsnSummaryGrid .slick-header-columns,
  .s-SalesRegisterGrid .slick-header-columns {
    white-space: normal;
    overflow: hidden;
    height: auto !important;
    align-items: center; /* Vertically centers the text */
  }

  .s-HsnSummaryGrid .slick-header-column,
  .s-SalesRegisterGrid .slick-header-column {
    overflow: visible;
    height: auto;
  }

  .s-HsnSummaryGrid .slick-header,
  .s-SalesRegisterGrid .slick-header {
    padding: 0;
    background-color: aliceblue;
  }

  .s-HsnSummaryGrid .slick-column-name,
  .s-SalesRegisterGrid .slick-column-name {
    white-space: normal;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
    line-height: 20px;
    max-height: 40px; /* Maximum height for two lines */
    min-height: 30px; /* Minimum height for a single line */
    word-wrap: break-word;
  }

  /*----- s-HsnSummaryGrid and SalesRegisterGrid  formatting ----- */

/*----- HsnSummary  ----- */


.field.Invoices .caption {
  display: none !important;
}

.s-HsnSummaryDetailsGridEditor .slick-header,
.s-HsnSummaryDetailsGridEditor .slick-header {
  padding: 0;
  background-color: aliceblue;
}







