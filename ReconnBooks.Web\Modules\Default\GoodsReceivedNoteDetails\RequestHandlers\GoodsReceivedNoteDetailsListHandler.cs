﻿using Serenity.Services;
using MyRequest = Serenity.Services.ListRequest;
using MyResponse = Serenity.Services.ListResponse<ReconnBooks.Default.GoodsReceivedNoteDetailsRow>;
using MyRow = ReconnBooks.Default.GoodsReceivedNoteDetailsRow;

namespace ReconnBooks.Default;

public interface IGoodsReceivedNoteDetailsListHandler : IListHandler<MyRow, MyRequest, MyResponse> {}

public class GoodsReceivedNoteDetailsListHandler : ListRequestHandler<MyRow, MyRequest, MyResponse>, IGoodsReceivedNoteDetailsListHandler
{
    public GoodsReceivedNoteDetailsListHandler(IRequestContext context)
            : base(context)
    {
    }
}