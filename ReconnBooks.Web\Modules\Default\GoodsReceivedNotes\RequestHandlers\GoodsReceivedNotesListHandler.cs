using Serenity.Services;
using MyRequest = Serenity.Services.ListRequest;
using MyResponse = Serenity.Services.ListResponse<ReconnBooks.Default.GoodsReceivedNotesRow>;
using MyRow = ReconnBooks.Default.GoodsReceivedNotesRow;

namespace ReconnBooks.Default;

public interface IGoodsReceivedNotesListHandler : IListHandler<MyRow, MyRequest, MyResponse> {}

public class GoodsReceivedNotesListHandler : ListRequestHandler<MyRow, MyRequest, MyResponse>, IGoodsReceivedNotesListHandler
{
    public GoodsReceivedNotesListHandler(IRequestContext context)
            : base(context)
    {
    }
}