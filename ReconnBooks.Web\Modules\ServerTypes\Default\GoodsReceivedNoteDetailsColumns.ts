﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { IndianNumberFormatter } from "../../Common/Helpers/IndianFormatNumbering";
import { GoodsReceivedNoteDetailsRow } from "./GoodsReceivedNoteDetailsRow";

export interface GoodsReceivedNoteDetailsColumns {
    RowNumber: Column<GoodsReceivedNoteDetailsRow>;
    CommodityName: Column<GoodsReceivedNoteDetailsRow>;
    CommodityCode: Column<GoodsReceivedNoteDetailsRow>;
    CommodityType: Column<GoodsReceivedNoteDetailsRow>;
    ReceivedQuantity: Column<GoodsReceivedNoteDetailsRow>;
    ReceivedUnitUnitName: Column<GoodsReceivedNoteDetailsRow>;
    PoQuantity: Column<GoodsReceivedNoteDetailsRow>;
    PoUnitUnitName: Column<GoodsReceivedNoteDetailsRow>;
    AcceptedQuantity: Column<GoodsReceivedNoteDetailsRow>;
    AcceptedUnitUnitName: Column<GoodsReceivedNoteDetailsRow>;
    Sku: Column<GoodsReceivedNoteDetailsRow>;
    SerialNos: Column<GoodsReceivedNoteDetailsRow>;
    SupplyDueDate: Column<GoodsReceivedNoteDetailsRow>;
    LocationName: Column<GoodsReceivedNoteDetailsRow>;
    WarehouseName: Column<GoodsReceivedNoteDetailsRow>;
    StoreName: Column<GoodsReceivedNoteDetailsRow>;
    RackNo: Column<GoodsReceivedNoteDetailsRow>;
    Remarks: Column<GoodsReceivedNoteDetailsRow>;
    CommodityDescription: Column<GoodsReceivedNoteDetailsRow>;
    PurchaseOrderDetailCommodityDescription: Column<GoodsReceivedNoteDetailsRow>;
    GoodsReceivedNoteNo: Column<GoodsReceivedNoteDetailsRow>;
    GoodsReceivedNoteDetailId: Column<GoodsReceivedNoteDetailsRow>;
}

export class GoodsReceivedNoteDetailsColumns extends ColumnsBase<GoodsReceivedNoteDetailsRow> {
    static readonly columnsKey = 'Default.GoodsReceivedNoteDetails';
    static readonly Fields = fieldsProxy<GoodsReceivedNoteDetailsColumns>();
}

[IndianNumberFormatter]; // referenced types