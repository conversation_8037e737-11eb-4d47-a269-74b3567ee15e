using ReconnBooks.Common.RowBehaviors;
using Serenity.ComponentModel;
using Serenity.Data;
using Serenity.Data.Mapping;
using System.ComponentModel;

namespace ReconnBooks.Default;

[<PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON><PERSON>("Default"), TableName("GoodsReceivedNoteTypes")]
[DisplayName("GoodsReceivedNote Types"), InstanceName("GoodsReceivedNote Types"), GenerateFields]
[ReadPermission("Administration:General")]
[ModifyPermission("Administration:General")]
[ServiceLookupPermission("Administration:General")]
public sealed partial class GoodsReceivedNoteTypesRow : Row<GoodsReceivedNoteTypesRow.RowFields>, IIdRow, INameRow, IRowNumberedRow
{
    //--Serial Numbering--

    [DisplayName("Sl.No."), NotMapped, Width(50), AlignCenter] // RowNumbering
    public long? RowNumber { get => fields.RowNumber[this]; set => fields.RowNumber[this] = value; }
    public Int64Field RowNumberField { get => fields.RowNumber; }

    [DisplayName("GoodsReceivedNote Type Id"), Column("GoodsReceivedNoteTypeId"), Identity, IdProperty]
    public int? GoodsReceivedNoteTypeId { get => fields.GoodsReceivedNoteTypeId[this]; set => fields.GoodsReceivedNoteTypeId[this] = value; }

    [DisplayName("GoodsReceivedNote Type Name"), Column("GoodsReceivedNoteTypeName"), Size(50), NotNull, QuickSearch, NameProperty]
    public string GoodsReceivedNoteTypeName { get => fields.GoodsReceivedNoteTypeName[this]; set => fields.GoodsReceivedNoteTypeName[this] = value; }

    [DisplayName("Description"), Size(500)]
    public string Description { get => fields.Description[this]; set => fields.Description[this] = value; }
}