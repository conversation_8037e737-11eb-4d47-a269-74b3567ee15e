using Serenity.ComponentModel;
using System.ComponentModel;

namespace ReconnBooks.Default.Columns;

[ColumnsScript("Default.GoodsReceivedNoteTypes")]
[BasedOnRow(typeof(GoodsReceivedNoteTypesRow), CheckNames = true)]
public class GoodsReceivedNoteTypesColumns
{
    //--Serial Numbering--

    [Width(50), AlignCenter]
    public long RowNumber { get; set; }

    [EditLink, DisplayName("GoodsReceivedNoteType Name"), Width(200)]
    public string GoodsReceivedNoteTypeName { get; set; }

    [EditLink, DisplayName("Description"), Width(250)]
    public string Description { get; set; }
 
    [EditLink, Width(50), DisplayName("Db.Shared.RecordId"), SortOrder(1, descending: false), AlignCenter]
    public int GoodsReceivedNoteTypeId { get; set; }
}