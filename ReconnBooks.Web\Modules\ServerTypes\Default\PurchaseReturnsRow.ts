﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";
import { PurchaseReturnDetailsRow } from "./PurchaseReturnDetailsRow";

export interface PurchaseReturnsRow {
    RowNumber?: number;
    PurchaseReturnId?: number;
    PurchaseReturnNo?: string;
    PurchaseReturnDate?: string;
    PurchaseReturnMonth?: string;
    VendorId?: number;
    VendorName?: string;
    PurchaseOrderId?: number;
    GoodsReceivedNoteId?: number;
    GoodsReceivedNoteNo?: string;
    FinancialYearId?: number;
    PurchaseReturnDetailsList?: PurchaseReturnDetailsRow[];
    UploadFiles?: string;
    Remarks?: string;
    ClientId?: number;
    ClientName?: string;
    PreparedByUserId?: number;
    PreparedDate?: string;
    VerifiedByUserId?: number;
    VerifiedDate?: string;
    AuthorizedByUserId?: number;
    AuthorizedDate?: string;
    ModifiedByUserId?: number;
    ModifiedDate?: string;
    CancelledByUserId?: number;
    CancelledDate?: string;
    AuthorizedStatus?: boolean;
    PurchaseOrderNo?: string;
    FinancialYearName?: string;
    PreparedByUserUsername?: string;
    VerifiedByUserUsername?: string;
    AuthorizedByUserUsername?: string;
    ModifiedByUserUsername?: string;
    CancelledByUserUsername?: string;
}

export abstract class PurchaseReturnsRow {
    static readonly idProperty = 'PurchaseReturnId';
    static readonly nameProperty = 'PurchaseReturnNo';
    static readonly localTextPrefix = 'Default.PurchaseReturns';
    static readonly lookupKey = 'Default.PurchaseReturns';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<PurchaseReturnsRow>('Default.PurchaseReturns') }
    static async getLookupAsync() { return getLookupAsync<PurchaseReturnsRow>('Default.PurchaseReturns') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<PurchaseReturnsRow>();
}