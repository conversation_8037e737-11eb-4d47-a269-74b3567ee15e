using Microsoft.AspNetCore.Mvc;
using Serenity.Data;
using Serenity.Reporting;
using Serenity.Services;
using Serenity.Web;
using System;
using System.Data;
using System.Globalization;
using Microsoft.Extensions.DependencyInjection;
using MyRow = ReconnBooks.Default.GoodsReceivedNotesRow;
using MimeKit;
using ReconnBooks.Modules.Common.Helpers.EmailHelper;

namespace ReconnBooks.Default.Endpoints;

[Route("Services/Default/GoodsReceivedNotes/[action]")]
[ConnectionKey(typeof(MyRow)), ServiceAuthorize(typeof(MyRow))]
public class GoodsReceivedNotesEndpoint : ServiceEndpoint
{
    private readonly IUserAccessor userAccessor;
    private readonly IUserRetrieveService userRetriever;
    private readonly ISqlConnections sqlConnections;
    private readonly IServiceProvider serviceProvider;
    private readonly ReportEmailHelper _emailHelper;

    public GoodsReceivedNotesEndpoint(IUserAccessor userAccessor, IUserRetrieveService userRetriever, ISqlConnections sqlConnections, IServiceProvider serviceProvider, ReportEmailHelper emailHelper)
    {
        this.userAccessor = userAccessor;
        this.userRetriever = userRetriever;
        this.sqlConnections = sqlConnections;
        this.serviceProvider = serviceProvider;
        this._emailHelper = emailHelper;
    }

    [HttpPost, AuthorizeCreate(typeof(MyRow))]
    public SaveResponse Create(IUnitOfWork uow, SaveRequest<MyRow> request,
        [FromServices] IGoodsReceivedNotesSaveHandler handler)
    {
        if (userAccessor.User?.GetUserDefinition(userRetriever) is not UserDefinition user)
        {
            return null;
        }

        var GoodsReceivedNotesNextNumber = GetNextNumber(uow.Connection, new GetNextNumberRequest
        {
            Prefix = request.Entity.GoodsReceivedNoteNo
        });
        request.Entity.GoodsReceivedNoteNo = GoodsReceivedNotesNextNumber.Serial;
        SaveResponse saveResponse = handler.Create(uow, request);

        return saveResponse;
    }

    [HttpPost, AuthorizeUpdate(typeof(MyRow))]
    public SaveResponse Update(IUnitOfWork uow, SaveRequest<MyRow> request,
        [FromServices] IGoodsReceivedNotesSaveHandler handler)
    {
        return handler.Update(uow, request);
    }

    [HttpPost, AuthorizeDelete(typeof(MyRow))]
    public DeleteResponse Delete(IUnitOfWork uow, DeleteRequest request,
        [FromServices] IGoodsReceivedNotesDeleteHandler handler)
    {
        return handler.Delete(uow, request);
    }

    [HttpPost]
    public RetrieveResponse<MyRow> Retrieve(IDbConnection connection, RetrieveRequest request,
        [FromServices] IGoodsReceivedNotesRetrieveHandler handler)
    {
        return handler.Retrieve(connection, request);
    }

    [HttpPost, AuthorizeList(typeof(MyRow))]
    public ListResponse<MyRow> List(IDbConnection connection, ListRequest request,
        [FromServices] IGoodsReceivedNotesListHandler handler)
    {
        return handler.List(connection, request);
    }

    [HttpPost, AuthorizeList(typeof(MyRow))]
    public FileContentResult ListExcel(IDbConnection connection, ListRequest request,
        [FromServices] IGoodsReceivedNotesListHandler handler,
        [FromServices] IExcelExporter exporter)
    {
        var data = List(connection, request, handler).Entities;
        var bytes = exporter.Export(data, typeof(Columns.GoodsReceivedNotesColumns), request.ExportColumns);
        return ExcelContentResult.Create(bytes, "GoodsReceivedNotesList_" +
            DateTime.Now.ToString("yyyyMMdd_HHmmss", CultureInfo.InvariantCulture) + ".xlsx");
    }
    public GetNextNumberResponse GetNextNumber(IDbConnection connection, GetNextNumberRequest request)
    {
        if (!string.IsNullOrWhiteSpace(request.Prefix))
        {
            request.Prefix = $"{request.Prefix}/";
        }

        request.Length = request.Prefix.Length + 3;

        return GetNextNumberHelper.GetNextNumber(connection, request, MyRow.Fields.GoodsReceivedNoteNo);
    }

    public ListResponse<GoodsReceivedNoteDetailsRow> GetFromPurchaseOrderDetails(RetrieveRequest request)
    {
        using (var connection = sqlConnections.NewByKey("Default"))
        {
            var purchaseOrderDetailsRowFields = PurchaseOrderDetailsRow.Fields;
            var purchaseOrderRetrieveHandler = serviceProvider.GetRequiredService<IPurchaseOrdersRetrieveHandler>();
            var purchaseOrdersRow = purchaseOrderRetrieveHandler.Retrieve(connection, new RetrieveRequest()
            {
                EntityId = request.EntityId
            });

            int id = 1;
            List<GoodsReceivedNoteDetailsRow> listGoodsReceivedNoteDetailsRow = new List<GoodsReceivedNoteDetailsRow>();

            var commoditiesRowFields = CommoditiesRow.Fields;
            purchaseOrdersRow.Entity.PurchaseOrderDetailsList.ForEach(q =>
            {
                var commodityRow = connection.Single<CommoditiesRow>(new Criteria(commoditiesRowFields.CommodityId) == q.CommodityId.GetValueOrDefault());
                listGoodsReceivedNoteDetailsRow.Add(new GoodsReceivedNoteDetailsRow
                {
                    RowNumber = id++,
                    CommodityId = q.CommodityId,
                    CommodityName = q.CommodityName,
                    CommodityCode = q.CommodityCode,
                    CommodityDescription = q.CommodityDescription,
                    CommodityTypeId = q.CommodityTypeId,
                    CommodityType = q.CommodityType,
                    PoUnitId = q.UnitId,
                    PoQuantity = q.Quantity,
                });
            });

            return new ListResponse<GoodsReceivedNoteDetailsRow>()
            {
                Entities = listGoodsReceivedNoteDetailsRow
            };
        }
    }
    
    [HttpPost]
    public ServiceResponse EmailGoodsReceivedNote(EmailRequest request)
    {
        var subject = "GoodsReceivedNote " + request.DocumentNo;
        var body = "Please find your GoodsReceivedNote attached. If you have any questions or concerns, please don't hesitate to reach out to us.";

        _emailHelper.SendReportEmail("GoodsReceivedNoteReport",
             request.DocumentId,
             request.ToEmail,
             request.DocumentNo,
             subject, body);

        return new ServiceResponse();
    }
}