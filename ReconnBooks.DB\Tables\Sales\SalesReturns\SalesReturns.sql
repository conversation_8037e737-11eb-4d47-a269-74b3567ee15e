﻿CREATE TABLE [dbo].[SalesReturns]
(
    [SalesReturnId]         INT				NOT NULL	IDENTITY (1, 1),
    [SalesReturnNo]         NVARCHAR (50)	NOT NULL,
    [SalesReturnDate]       DATETIME		    NULL,

    [CustomerId]			INT				NOT NULL,
    [DeliveryNoteId]		INT				    NULL,
    [InvoiceId]			    INT				    NULL,
    [GoodsReceivedNoteId]				    INT				    NULL,
    [FinancialYearId]		INT				    NULL,

    [UploadFiles]           NVARCHAR (500)	    NULL,
    [Remarks]		        NVARCHAR (MAX)	    NULL,

    -------------------Authorization Details-------------
    [ClientId]              INT	        NOT NULL    CONSTRAINT [DF_SalesReturns_ClientId]	DEFAULT ((0)),
    [PreparedByUserId]      INT	            NULL,
    [PreparedDate]          DATETIME        NULL,
    [VerifiedByUserId]      INT             NULL,
    [VerifiedDate]          DATETIME        NULL,
    [AuthorizedByUserId]    INT             NULL,
    [AuthorizedDate]        DATETIME        NULL,
    [ModifiedByUserId]      INT             NULL,
    [ModifiedDate]          DATETIME        NULL,
    [CancelledByUserId]     INT             NULL,
    [CancelledDate]			DATETIME        NULL,
    [AuthorizedStatus]      BIT         NOT NULL    DEFAULT ((0)),
    -------------------Authorization Details----------------------
    CONSTRAINT [FK_SalesReturns_PreparedByUsers]     FOREIGN KEY ([PreparedByUserId])    REFERENCES	[dbo].[Users]	([UserId]),
    CONSTRAINT [FK_SalesReturns_VerfiedByUsers]      FOREIGN KEY ([VerifiedByUserId])	REFERENCES	[dbo].[Users]	([UserId]),
    CONSTRAINT [FK_SalesReturns_AuthorizedByUsers]   FOREIGN KEY ([AuthorizedByUserId])	REFERENCES	[dbo].[Users]	([UserId]),
    CONSTRAINT [FK_SalesReturns_ModifiedByUsers]     FOREIGN KEY ([ModifiedByUserId])	REFERENCES	[dbo].[Users]	([UserId]),
    CONSTRAINT [FK_SalesReturns_CancelledByUsers]    FOREIGN KEY ([CancelledByUserId])	REFERENCES	[dbo].[Users]	([UserId]),
    CONSTRAINT [FK_SalesReturns_Clients]	         FOREIGN KEY ([ClientId])	        REFERENCES	[dbo].[Clients] ([ClientId]),
    -------------------Authorization Details End------------------
    
	CONSTRAINT [PK_SalesReturns] PRIMARY KEY    CLUSTERED   ([SalesReturnId] ASC),
    CONSTRAINT [FK_SalesReturns_Customers]		FOREIGN KEY	([CustomerId])      REFERENCES [dbo].[Customers]	    ([CustomerId]),
    CONSTRAINT [FK_SalesReturns_DeliveryNotes]  FOREIGN KEY	([DeliveryNoteId])	REFERENCES [dbo].[DeliveryNotes]	([DeliveryNoteId]),
    CONSTRAINT [FK_SalesReturns_Invoices]		FOREIGN KEY	([InvoiceId])		REFERENCES [dbo].[Invoices]	    	([InvoiceId]),
    CONSTRAINT [FK_SalesReturns_GoodsReceivedNotes]		    FOREIGN KEY	([GoodsReceivedNoteId])			REFERENCES [dbo].[GoodsReceivedNotes]	    	    ([GoodsReceivedNoteId]),
    CONSTRAINT [FK_SalesReturns_FinancialYears] FOREIGN KEY	([FinancialYearId])	REFERENCES [dbo].[FinancialYears]	([FinancialYearId]),
);

GO
CREATE NONCLUSTERED INDEX [CustomerId]
    ON [dbo].[SalesReturns]([CustomerId] ASC);

GO
CREATE NONCLUSTERED INDEX [InvoiceId]
    ON [dbo].[SalesReturns]([InvoiceId] ASC);

GO
CREATE NONCLUSTERED INDEX [DeliveryNotes]
    ON [dbo].[SalesReturns]([DeliveryNoteId] ASC);

GO
CREATE NONCLUSTERED INDEX [FinancialYears]
    ON [dbo].[SalesReturns]([FinancialYearId] ASC);

GO
CREATE NONCLUSTERED INDEX [Clients]
    ON [dbo].[SalesReturns]([ClientId] ASC);
