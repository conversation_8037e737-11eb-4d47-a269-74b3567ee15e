﻿import { ServiceLookupEditor, TextAreaEditor, StringEditor, DecimalEditor, LookupEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { CommoditiesDialog } from "../../Default/Commodities/CommoditiesDialog";
import { CommodityCodeEditor } from "../../Default/Commodities/CommodityCodeEditor";
import { RejectionReasonsDialog } from "../../Default/RejectionReasons/RejectionReasonsDialog";

export interface PurchaseReturnDetailsForm {
    CommodityTypeId: ServiceLookupEditor;
    CommodityCode: CommodityCodeEditor;
    CommodityId: ServiceLookupEditor;
    CommodityDescription: TextAreaEditor;
    HSNSACCode: StringEditor;
    HSNSACGroup: StringEditor;
    HSNSACDescription: TextAreaEditor;
    RejectedQuantity: DecimalEditor;
    UnitId: LookupEditor;
    RejectedItemSerialNo: StringEditor;
    RejectedAmount: DecimalEditor;
    RejectionReasonId: ServiceLookupEditor;
    AssessmentRemarks: StringEditor;
    ReplacementMethodId: ServiceLookupEditor;
    Remarks: StringEditor;
    PurchaseOrderDetailId: ServiceLookupEditor;
    GoodsReceivedNoteDetailId: ServiceLookupEditor;
}

export class PurchaseReturnDetailsForm extends PrefixedContext {
    static readonly formKey = 'Default.PurchaseReturnDetails';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!PurchaseReturnDetailsForm.init)  {
            PurchaseReturnDetailsForm.init = true;

            var w0 = ServiceLookupEditor;
            var w1 = CommodityCodeEditor;
            var w2 = TextAreaEditor;
            var w3 = StringEditor;
            var w4 = DecimalEditor;
            var w5 = LookupEditor;

            initFormType(PurchaseReturnDetailsForm, [
                'CommodityTypeId', w0,
                'CommodityCode', w1,
                'CommodityId', w0,
                'CommodityDescription', w2,
                'HSNSACCode', w3,
                'HSNSACGroup', w3,
                'HSNSACDescription', w2,
                'RejectedQuantity', w4,
                'UnitId', w5,
                'RejectedItemSerialNo', w3,
                'RejectedAmount', w4,
                'RejectionReasonId', w0,
                'AssessmentRemarks', w3,
                'ReplacementMethodId', w0,
                'Remarks', w3,
                'PurchaseOrderDetailId', w0,
                'GoodsReceivedNoteDetailId', w0
            ]);
        }
    }
}

queueMicrotask(() => [CommoditiesDialog, RejectionReasonsDialog]); // referenced dialogs