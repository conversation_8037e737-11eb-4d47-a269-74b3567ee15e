import { GoodsReceivedNoteTypesForm, GoodsReceivedNoteTypesRow, GoodsReceivedNoteTypesService } from '@/ServerTypes/Default';
import { Decorators } from '@serenity-is/corelib';
import { PendingChangesConfirmDialog } from '../../Common/Helpers/PendingChangesConfirmDialog';

@Decorators.registerClass('ReconnBooks.Default.GoodsReceivedNoteTypesDialog')
export class GoodsReceivedNoteTypesDialog extends PendingChangesConfirmDialog<GoodsReceivedNoteTypesRow> {
    protected getFormKey() { return GoodsReceivedNoteTypesForm.formKey; }
    protected getRowDefinition() { return GoodsReceivedNoteTypesRow; }
    protected getService() { return GoodsReceivedNoteTypesService.baseUrl; }

    protected form = new GoodsReceivedNoteTypesForm(this.idPrefix);

    protected async afterLoadEntity() {
        super.afterLoadEntity();
        this.setDialogsLoadedState();
    }
}