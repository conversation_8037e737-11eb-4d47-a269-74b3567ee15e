﻿using Serenity.Services;
using MyRequest = Serenity.Services.DeleteRequest;
using MyResponse = Serenity.Services.DeleteResponse;
using MyRow = ReconnBooks.Default.GoodsReceivedNoteTypesRow;

namespace ReconnBooks.Default;

public interface IGoodsReceivedNoteTypesDeleteHandler : <PERSON>eleteHandler<MyRow, MyRequest, MyResponse> {}

public class GoodsReceivedNoteTypesDeleteHandler : DeleteRequestHandler<MyRow, MyRequest, MyResponse>, IGoodsReceivedNoteTypesDeleteHandler
{
    public GoodsReceivedNoteTypesDeleteHandler(IRequestContext context)
            : base(context)
    {
    }
}