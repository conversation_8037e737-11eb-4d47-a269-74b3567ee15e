using Microsoft.AspNetCore.Mvc;
using Serenity.Web;

namespace ReconnBooks.Default.Pages;

[PageAuthorize(typeof(GoodsReceivedNotesRow))]
public class GoodsReceivedNotesPage : Controller
{
    [Route("Default/GoodsReceivedNotes")]
    public ActionResult Index()
    {
        return this.GridPage("@/Default/GoodsReceivedNotes/GoodsReceivedNotesPage",
            GoodsReceivedNotesRow.Fields.PageTitle());
    }
}