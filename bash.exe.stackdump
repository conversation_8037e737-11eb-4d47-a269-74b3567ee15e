Stack trace:
Frame         Function      Args
0007FFFF8E80  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF7D80) msys-2.0.dll+0x2118E
0007FFFF8E80  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFF9158) msys-2.0.dll+0x69BA
0007FFFF8E80  0002100469F2 (00021028DF99, 0007FFFF8D38, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF8E80  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF8E80  00021006A545 (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFF9160  00021006B9A5 (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF987C60000 ntdll.dll
7FF9867E0000 KERNEL32.DLL
7FF985650000 KERNELBASE.dll
7FF985D30000 USER32.dll
7FF984DF0000 win32u.dll
7FF9860A0000 GDI32.dll
7FF985270000 gdi32full.dll
7FF9851C0000 msvcp_win.dll
7FF985470000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF985A20000 advapi32.dll
7FF986610000 msvcrt.dll
7FF986700000 sechost.dll
7FF986D10000 RPCRT4.dll
7FF984410000 CRYPTBASE.DLL
7FF984E20000 bcryptPrimitives.dll
7FF9866C0000 IMM32.DLL
