#nullable enable
using System.Diagnostics;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Serenity.Abstractions;
using ReconnBooks.Common.ChatSql.Models;

namespace ReconnBooks.Common.ChatSql.Services
{
    public interface IChatSqlService
    {
        Task<NaturalLanguageSqlResponse> ProcessQueryAsync(NaturalLanguageSqlRequest request);
        Task<DatabaseSchemaInfo> GetSchemaAsync();
        Task RefreshSchemaAsync();
        bool IsServiceAvailable();
    }

    public class ChatSqlService : IChatSqlService
    {
        private readonly IDatabaseSchemaService _schemaService;
        private readonly IAiSqlService _aiService;
        private readonly ISqlExecutionService _executionService;
        private readonly ITwoLevelCache _cache;
        private readonly IOptions<NaturalLanguageSqlConfiguration> _config;
        private readonly ILogger<ChatSqlService> _logger;

        private const string CONVERSATION_CACHE_PREFIX = "ChatSql_Conversation_";
        private static readonly TimeSpan ConversationCacheExpiration = TimeSpan.FromHours(2);

        public ChatSqlService(
            IDatabaseSchemaService schemaService,
            IAiSqlService aiService,
            ISqlExecutionService executionService,
            ITwoLevelCache cache,
            IOptions<NaturalLanguageSqlConfiguration> config,
            ILogger<ChatSqlService> logger)
        {
            _schemaService = schemaService;
            _aiService = aiService;
            _executionService = executionService;
            _cache = cache;
            _config = config;
            _logger = logger;
        }

        public async Task<NaturalLanguageSqlResponse> ProcessQueryAsync(NaturalLanguageSqlRequest request)
        {
            var stopwatch = Stopwatch.StartNew();
            var response = new NaturalLanguageSqlResponse
            {
                ConversationId = request.ConversationId ?? Guid.NewGuid().ToString()
            };

            try
            {
                _logger.LogInformation("Processing chat SQL request: {Message}", request.Message);

                // Check if AI service is available
                if (!_aiService.IsServiceAvailable())
                {
                    response.Success = false;
                    response.ErrorMessage = "AI service is not available. Please check configuration.";
                    return response;
                }

                // Get database schema
                var schema = await _schemaService.GetSchemaAsTextAsync();

                // Get conversation context
                var context = GetConversationContext(response.ConversationId);

                // Convert natural language to SQL using AI
                var generatedSql = await _aiService.ConvertToSqlAsync(request.Message, schema, context);
                response.GeneratedSql = generatedSql;

                // Validate the generated SQL
                var validationResult = await _executionService.ValidateQueryAsync(generatedSql);
                if (!validationResult.IsValid)
                {
                    response.Success = false;
                    response.ErrorMessage = $"Generated query validation failed: {validationResult.ErrorMessage}";

                    // Save failed attempt to conversation
                    SaveToConversation(response.ConversationId, new ReconnChatMessage
                    {
                        Role = "user",
                        Content = request.Message,
                        Timestamp = DateTime.UtcNow
                    });

                    SaveToConversation(response.ConversationId, new ReconnChatMessage
                    {
                        Role = "assistant",
                        Content = $"I couldn't generate a valid SQL query for your request: {validationResult.ErrorMessage}",
                        Timestamp = DateTime.UtcNow,
                        SqlQuery = generatedSql
                    });

                    return response;
                }

                // Use modified query if validation made changes
                var finalQuery = validationResult.ModifiedQuery ?? generatedSql;
                response.GeneratedSql = finalQuery;

                // Execute the SQL query
                var queryResult = await _executionService.ExecuteQueryAsync(finalQuery);
                response.QueryResult = queryResult;

                // Generate metadata
                stopwatch.Stop();
                response.Metadata = _executionService.GetQueryMetadata(
                    finalQuery, queryResult, (int)stopwatch.ElapsedMilliseconds);

                if (validationResult.WasModified)
                {
                    response.Metadata.WasQueryModified = true;
                    response.Metadata.OriginalQuery = generatedSql;
                    response.Metadata.ModificationReason = validationResult.ModificationReason;
                }

                response.Success = true;

                // Save successful interaction to conversation
                SaveToConversation(response.ConversationId, new ReconnChatMessage
                {
                    Role = "user",
                    Content = request.Message,
                    Timestamp = DateTime.UtcNow
                });

                SaveToConversation(response.ConversationId, new ReconnChatMessage
                {
                    Role = "assistant",
                    Content = $"I found {response.Metadata.RowCount} results for your query.",
                    Timestamp = DateTime.UtcNow,
                    SqlQuery = finalQuery,
                    QueryResult = queryResult
                });

                _logger.LogInformation("Chat SQL request processed successfully. Execution time: {ExecutionTime}ms, Rows: {RowCount}",
                    stopwatch.ElapsedMilliseconds, response.Metadata.RowCount);

                return response;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "Error processing chat SQL request: {Message}", request.Message);

                response.Success = false;
                response.ErrorMessage = $"An error occurred while processing your request: {ex.Message}";

                // Save error to conversation
                SaveToConversation(response.ConversationId, new ReconnChatMessage
                {
                    Role = "user",
                    Content = request.Message,
                    Timestamp = DateTime.UtcNow
                });

                SaveToConversation(response.ConversationId, new ReconnChatMessage
                {
                    Role = "assistant",
                    Content = $"I encountered an error while processing your request: {ex.Message}",
                    Timestamp = DateTime.UtcNow
                });

                return response;
            }
        }

        public async Task<DatabaseSchemaInfo> GetSchemaAsync()
        {
            return await _schemaService.GetSchemaAsync();
        }

        public async Task RefreshSchemaAsync()
        {
            await _schemaService.RefreshSchemaAsync();
        }

        public bool IsServiceAvailable()
        {
            return _aiService.IsServiceAvailable();
        }

        private ConversationContext? GetConversationContext(string conversationId)
        {
            try
            {
                var cacheKey = CONVERSATION_CACHE_PREFIX + conversationId;
                return _cache.GetLocalStoreOnly<ConversationContext>(cacheKey, ConversationCacheExpiration, "", () => null);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error retrieving conversation context for {ConversationId}", conversationId);
                return null;
            }
        }

        private void SaveToConversation(string conversationId, ReconnChatMessage message)
        {
            try
            {
                var cacheKey = CONVERSATION_CACHE_PREFIX + conversationId;
                var context = GetConversationContext(conversationId) ?? new ConversationContext
                {
                    ConversationId = conversationId,
                    CreatedAt = DateTime.UtcNow,
                    Messages = new List<ReconnChatMessage>()
                };

                context.Messages.Add(message);
                context.LastActivity = DateTime.UtcNow;

                // Keep only the last 20 messages to prevent cache bloat
                if (context.Messages.Count > 20)
                {
                    context.Messages = context.Messages.TakeLast(20).ToList();
                }

                _cache.SetLocalStoreOnly(cacheKey, ConversationCacheExpiration, "", context);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error saving to conversation {ConversationId}", conversationId);
            }
        }
    }
}
