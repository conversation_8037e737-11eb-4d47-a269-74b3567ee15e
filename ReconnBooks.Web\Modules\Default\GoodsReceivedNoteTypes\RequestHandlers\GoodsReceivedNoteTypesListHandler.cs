﻿using Serenity.Services;
using MyRequest = Serenity.Services.ListRequest;
using MyResponse = Serenity.Services.ListResponse<ReconnBooks.Default.GoodsReceivedNoteTypesRow>;
using MyRow = ReconnBooks.Default.GoodsReceivedNoteTypesRow;

namespace ReconnBooks.Default;

public interface IGoodsReceivedNoteTypesListHandler : IListHandler<MyRow, MyRequest, MyResponse> {}

public class GoodsReceivedNoteTypesListHandler : ListRequestHandler<MyRow, MyRequest, MyResponse>, IGoodsReceivedNoteTypesListHandler
{
    public GoodsReceivedNoteTypesListHandler(IRequestContext context)
            : base(context)
    {
    }
}