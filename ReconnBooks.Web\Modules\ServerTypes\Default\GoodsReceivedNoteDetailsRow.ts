﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface GoodsReceivedNoteDetailsRow {
    RowNumber?: number;
    GoodsReceivedNoteDetailId?: number;
    GoodsReceivedNoteId?: number;
    PurchaseOrderDetailId?: number;
    CommodityTypeId?: number;
    CommodityId?: number;
    CommodityCode?: string;
    CommodityDescription?: string;
    CommodityType?: string;
    CommodityName?: string;
    PoQuantity?: number;
    PoUnitId?: number;
    ReceivedQuantity?: number;
    ReceivedUnitId?: number;
    Sku?: string;
    SerialNos?: string;
    AcceptedQuantity?: number;
    AcceptedUnitId?: number;
    SupplyDueDate?: string;
    LocationId?: number;
    WarehouseId?: number;
    StoreId?: number;
    RackId?: number;
    Remarks?: string;
    ClientId?: number;
    ClientName?: string;
    GoodsReceivedNoteNo?: string;
    PurchaseOrderDetailCommodityDescription?: string;
    PoUnitUnitName?: string;
    ReceivedUnitUnitName?: string;
    AcceptedUnitUnitName?: string;
    LocationName?: string;
    WarehouseName?: string;
    StoreName?: string;
    RackNo?: string;
}

export abstract class GoodsReceivedNoteDetailsRow {
    static readonly idProperty = 'GoodsReceivedNoteDetailId';
    static readonly nameProperty = 'GoodsReceivedNoteDetailId';
    static readonly localTextPrefix = 'Default.GoodsReceivedNoteDetails';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<GoodsReceivedNoteDetailsRow>();
}