﻿CREATE TABLE [dbo].[PurchaseReturnDetails] (
    [PurchaseReturnDetailId] INT             IDENTITY (1, 1) NOT NULL,
    [PurchaseReturnId]       INT             NOT NULL,
    [PurchaseOrderDetailId]  INT             NULL,
    [GoodsReceivedNoteDetailId]            INT             NULL,
    
    [CommodityTypeId]        INT             NOT NULL,
    [CommodityId]            BIGINT          NOT NULL,
    [CommodityDescription]   NVARCHAR (MAX)  NULL,
    
    [RejectedQuantity]       DECIMAL (18, 2) NOT NULL,
    [UnitId]                 INT             NOT NULL,
    [RejectedAmount]         DECIMAL (18, 2) NULL,
    [RejectedItemSerialNo]   NVARCHAR (MAX)  NULL,
    [RejectionReasonId]      INT             NULL,
    
    [AssessmentRemarks]      NVARCHAR (MAX)  NULL,
    [ReplacementMethodId]    INT             NULL,
    [Remarks]                NVARCHAR (MAX)  NULL,
    
    CONSTRAINT [PK_PurchaseReturnDetails] PRIMARY KEY CLUSTERED ([PurchaseReturnDetailId] ASC),
    CONSTRAINT [FK_PurchaseReturnDetails_PurchaseReturns] FOREIGN KEY ([PurchaseReturnId]) REFERENCES [dbo].[PurchaseReturns] ([PurchaseReturnId]),
    CONSTRAINT [FK_PurchaseReturnDetails_GoodsReceivedNoteDetails] FOREIGN KEY ([GoodsReceivedNoteDetailId]) REFERENCES [dbo].[GoodsReceivedNoteDetails] ([GoodsReceivedNoteDetailId]),
    CONSTRAINT [FK_PurchaseReturnDetails_Commodities] FOREIGN KEY ([CommodityId]) REFERENCES [dbo].[Commodities] ([CommodityId]),
    
    CONSTRAINT [FK_PurchaseReturnDetails_RejectionReasons] FOREIGN KEY ([RejectionReasonId]) REFERENCES [dbo].[RejectionReasons] ([RejectionReasonId]),
    CONSTRAINT [FK_PurchaseReturnDetails_ReplacementMethods] FOREIGN KEY ([ReplacementMethodId]) REFERENCES [dbo].[ReplacementMethods] ([ReplacementMethodId]),
    CONSTRAINT [FK_PurchaseReturnDetails_PurchaseOrderDetails] FOREIGN KEY ([PurchaseOrderDetailId]) REFERENCES [dbo].[PurchaseOrderDetails] ([PurchaseOrderDetailId]),
    CONSTRAINT [FK_PurchaseReturnDetails_Units] FOREIGN KEY ([UnitId]) REFERENCES [dbo].[Units] ([UnitId])
);


GO
CREATE NONCLUSTERED INDEX [PurchaseReturns]
    ON [dbo].[PurchaseReturnDetails]([PurchaseReturnId] ASC);


GO
CREATE NONCLUSTERED INDEX [PurchaseOrderDetails]
    ON [dbo].[PurchaseReturnDetails]([PurchaseOrderDetailId] ASC);


GO
CREATE NONCLUSTERED INDEX [GoodsReceivedNoteDetails]
    ON [dbo].[PurchaseReturnDetails]([GoodsReceivedNoteDetailId] ASC);


GO
CREATE NONCLUSTERED INDEX [Commodities]
    ON [dbo].[PurchaseReturnDetails]([CommodityId] ASC);

