﻿import { StringEditor, DateEditor, ServiceLookupEditor, LookupEditor, IntegerEditor, TextAreaEditor, BooleanEditor, MultipleImageUploadEditor, DateTimeEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { GoodsReceivedNoteDetailsGridEditor } from "../../Default/GoodsReceivedNoteDetails/GoodsReceivedNoteDetailsGridEditor";
import { GoodsReceivedNoteTypesDialog } from "../../Default/GoodsReceivedNoteTypes/GoodsReceivedNoteTypesDialog";
import { PurchaseOrdersDialog } from "../../Default/PurchaseOrders/PurchaseOrdersDialog";
import { VendorsDialog } from "../../Default/Vendors/VendorsDialog";

export interface GoodsReceivedNotesForm {
    GoodsReceivedNoteNo: StringEditor;
    GoodsReceivedNoteDate: DateEditor;
    VendorId: ServiceLookupEditor;
    GoodsReceivedNoteTypeId: ServiceLookupEditor;
    GSTIN: StringEditor;
    PurchaseOrderId: ServiceLookupEditor;
    FinancialYearId: LookupEditor;
    VendorDcInvoiceNo: StringEditor;
    VendorDcInvoiceDate: DateEditor;
    GoodsReceivedNoteDetailsList: GoodsReceivedNoteDetailsGridEditor;
    ShippedThrough: StringEditor;
    DeliveryAddress: StringEditor;
    DeliveryCityId: LookupEditor;
    DeliveryPinCode: IntegerEditor;
    ShippingDocketNo: StringEditor;
    ReceivedByEmployeeId: LookupEditor;
    VehicleNo: StringEditor;
    VehicleType: StringEditor;
    GatePassNo: StringEditor;
    GatePassDate: DateEditor;
    Inspection: StringEditor;
    Remarks: TextAreaEditor;
    AuthorizedStatus: BooleanEditor;
    UploadDocuments: MultipleImageUploadEditor;
    PreparedByUserId: LookupEditor;
    PreparedDate: DateTimeEditor;
    VerifiedByUserId: LookupEditor;
    VerifiedDate: DateTimeEditor;
    AuthorizedByUserId: LookupEditor;
    AuthorizedDate: DateTimeEditor;
    ModifiedByUserId: LookupEditor;
    ModifiedDate: DateEditor;
    CancelledByUserId: LookupEditor;
    CancelledDate: DateEditor;
}

export class GoodsReceivedNotesForm extends PrefixedContext {
    static readonly formKey = 'Default.GoodsReceivedNotes';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!GoodsReceivedNotesForm.init)  {
            GoodsReceivedNotesForm.init = true;

            var w0 = StringEditor;
            var w1 = DateEditor;
            var w2 = ServiceLookupEditor;
            var w3 = LookupEditor;
            var w4 = GoodsReceivedNoteDetailsGridEditor;
            var w5 = IntegerEditor;
            var w6 = TextAreaEditor;
            var w7 = BooleanEditor;
            var w8 = MultipleImageUploadEditor;
            var w9 = DateTimeEditor;

            initFormType(GoodsReceivedNotesForm, [
                'GoodsReceivedNoteNo', w0,
                'GoodsReceivedNoteDate', w1,
                'VendorId', w2,
                'GoodsReceivedNoteTypeId', w2,
                'GSTIN', w0,
                'PurchaseOrderId', w2,
                'FinancialYearId', w3,
                'VendorDcInvoiceNo', w0,
                'VendorDcInvoiceDate', w1,
                'GoodsReceivedNoteDetailsList', w4,
                'ShippedThrough', w0,
                'DeliveryAddress', w0,
                'DeliveryCityId', w3,
                'DeliveryPinCode', w5,
                'ShippingDocketNo', w0,
                'ReceivedByEmployeeId', w3,
                'VehicleNo', w0,
                'VehicleType', w0,
                'GatePassNo', w0,
                'GatePassDate', w1,
                'Inspection', w0,
                'Remarks', w6,
                'AuthorizedStatus', w7,
                'UploadDocuments', w8,
                'PreparedByUserId', w3,
                'PreparedDate', w9,
                'VerifiedByUserId', w3,
                'VerifiedDate', w9,
                'AuthorizedByUserId', w3,
                'AuthorizedDate', w9,
                'ModifiedByUserId', w3,
                'ModifiedDate', w1,
                'CancelledByUserId', w3,
                'CancelledDate', w1
            ]);
        }
    }
}

queueMicrotask(() => [VendorsDialog, GoodsReceivedNoteTypesDialog, PurchaseOrdersDialog]); // referenced dialogs