﻿import { StringEditor, DateEditor, ServiceLookupEditor, LookupEditor, BooleanEditor, TextAreaEditor, DateTimeEditor, MultipleImageUploadEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { GoodsReceivedNotesDialog } from "../../Default/GoodsReceivedNotes/GoodsReceivedNotesDialog";
import { PurchaseOrdersDialog } from "../../Default/PurchaseOrders/PurchaseOrdersDialog";
import { PurchaseReturnDetailsGridEditor } from "../../Default/PurchaseReturnDetails/PurchaseReturnDetailsGridEditor";
import { VendorsDialog } from "../../Default/Vendors/VendorsDialog";

export interface PurchaseReturnsForm {
    PurchaseReturnNo: StringEditor;
    PurchaseReturnDate: DateEditor;
    VendorId: ServiceLookupEditor;
    PurchaseOrderId: ServiceLookupEditor;
    GoodsReceivedNoteId: ServiceLookupEditor;
    PurchaseReturnDetailsList: PurchaseReturnDetailsGridEditor;
    FinancialYearId: LookupEditor;
    AuthorizedStatus: BooleanEditor;
    Remarks: TextAreaEditor;
    PreparedByUserId: LookupEditor;
    PreparedDate: DateTimeEditor;
    VerifiedByUserId: LookupEditor;
    VerifiedDate: DateTimeEditor;
    AuthorizedByUserId: LookupEditor;
    AuthorizedDate: DateTimeEditor;
    ModifiedByUserId: LookupEditor;
    ModifiedDate: DateEditor;
    CancelledByUserId: LookupEditor;
    CancelledDate: DateEditor;
    UploadFiles: MultipleImageUploadEditor;
}

export class PurchaseReturnsForm extends PrefixedContext {
    static readonly formKey = 'Default.PurchaseReturns';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!PurchaseReturnsForm.init)  {
            PurchaseReturnsForm.init = true;

            var w0 = StringEditor;
            var w1 = DateEditor;
            var w2 = ServiceLookupEditor;
            var w3 = PurchaseReturnDetailsGridEditor;
            var w4 = LookupEditor;
            var w5 = BooleanEditor;
            var w6 = TextAreaEditor;
            var w7 = DateTimeEditor;
            var w8 = MultipleImageUploadEditor;

            initFormType(PurchaseReturnsForm, [
                'PurchaseReturnNo', w0,
                'PurchaseReturnDate', w1,
                'VendorId', w2,
                'PurchaseOrderId', w2,
                'GoodsReceivedNoteId', w2,
                'PurchaseReturnDetailsList', w3,
                'FinancialYearId', w4,
                'AuthorizedStatus', w5,
                'Remarks', w6,
                'PreparedByUserId', w4,
                'PreparedDate', w7,
                'VerifiedByUserId', w4,
                'VerifiedDate', w7,
                'AuthorizedByUserId', w4,
                'AuthorizedDate', w7,
                'ModifiedByUserId', w4,
                'ModifiedDate', w1,
                'CancelledByUserId', w4,
                'CancelledDate', w1,
                'UploadFiles', w8
            ]);
        }
    }
}

queueMicrotask(() => [VendorsDialog, PurchaseOrdersDialog, GoodsReceivedNotesDialog]); // referenced dialogs