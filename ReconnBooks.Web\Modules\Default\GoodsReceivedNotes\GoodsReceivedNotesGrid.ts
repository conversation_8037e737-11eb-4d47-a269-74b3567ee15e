import { GoodsReceivedNotesColumns, GoodsReceivedNotesRow, GoodsReceivedNotesService } from '@/ServerTypes/Default';
import { Decorators, EntityGrid, LookupEditor, WidgetProps, notifySuccess, notifyError } from '@serenity-is/corelib';
import { FinancialYearHelper } from '../FinancialYears/FinancialYearHelper';
import { ExcelExportHelper, PdfExportHelper, ReportHelper } from "@serenity-is/extensions";
import { HeaderFiltersMixin } from "@serenity-is/pro.extensions";
import { GoodsReceivedNotesDialog } from './GoodsReceivedNotesDialog';

@Decorators.registerClass('ReconnBooks.Default.GoodsReceivedNotesGrid')
@Decorators.filterable()
export class GoodsReceivedNotesGrid extends EntityGrid<GoodsReceivedNotesRow, any> {
    protected getColumnsKey() { return GoodsReceivedNotesColumns.columnsKey; }
    protected getDialogType() { return GoodsReceivedNotesDialog; }
    protected getRowDefinition() { return GoodsReceivedNotesRow; }
    protected getService() { return GoodsReceivedNotesService.baseUrl; }

    constructor(props: WidgetProps<any>) {
        super(props);

        new HeaderFiltersMixin({
            grid: this
        });
    }

    protected async createQuickFilters(): Promise<void> {
        await super.createQuickFilters();

        const currentFinancialYearId = await FinancialYearHelper.getCurrentFinancialYearId();
        if (currentFinancialYearId) {
            this.findQuickFilter(LookupEditor, "FinancialYearId").values = [currentFinancialYearId.toString()];
        }
    }

    protected getColumns() {
        var columns = super.getColumns();

        columns.splice(1, 0, {
            id: 'Print GoodsReceivedNotes',
            field: null,
            name: '',
            cssClass: 'align-center',
            headerCssClass: 'no-header-filter',
            format: _ => `<a class="inline-action" data-action="print-GoodsReceivedNotes" title="GoodsReceivedNotes"><i class="fas fa-file-pdf" style="color: red"></i></a>`,
            width: 20,
            minWidth: 20,
            maxWidth: 20
        });

        columns.splice(2, 0, {
            id: 'Email GoodsReceivedNotes',
            field: null,
            name: '',
            cssClass: 'align-center',
            headerCssClass: 'no-header-filter',
            format: _ => `<a class="inline-action" data-action="email-GoodsReceivedNotes" title="email GoodsReceivedNotes"><i class="fas fa-envelope" style="color: #4CAF50"></i></a>`,
            width: 20,
            minWidth: 20,
            maxWidth: 20
        });

        return columns;
    }

    protected onClick(e: Event, row: number, cell: number) {
        super.onClick(e, row, cell);

        //if (Fluent.isDefaultPrevented(e))
        //    return;

        var item = this.itemAt(row);
        let action = (e.target as HTMLElement)?.closest(".inline-action")?.getAttribute("data-action");
        if (action) {
            e.preventDefault();
            if (action == "print-GoodsReceivedNotes") {
                ReportHelper.execute({
                    reportKey: 'GoodsReceivedNoteReport',
                    params: {
                        ID: item.GoodsReceivedNoteId
                    }
                });
            }
            else if (action == "email-GoodsReceivedNotes") {
                this.emailGoodsReceivedNotes(item);
            }
        }
    }

    private async emailGoodsReceivedNotes(item: GoodsReceivedNotesRow) {
        try {
            await GoodsReceivedNotesService.EmailGoodsReceivedNote({
                DocumentId: item.GoodsReceivedNoteId,
                DocumentNo: item.GoodsReceivedNoteNo,
                ToEmail: item.VendorEMailId
            });
            notifySuccess("GoodsReceivedNote has been emailed successfully");
        }
        catch (e) {
            notifyError(e.message);
        }
    }

    protected getButtons() {
        var buttons = super.getButtons();
        buttons.push(ExcelExportHelper.createToolButton({
            grid: this,
            onViewSubmit: () => this.onViewSubmit(),
            service: GoodsReceivedNotesService.baseUrl + '/ListExcel',
            separator: true,
            hint: "",
            title: "Excel"
        }));
        buttons.push(PdfExportHelper.createToolButton({
            grid: this,
            onViewSubmit: () => this.onViewSubmit(),
            title: "PDF"
        }));
        return buttons;
    }
}