import { GoodsReceivedNoteTypesColumns, GoodsReceivedNoteTypesRow, GoodsReceivedNoteTypesService } from '@/ServerTypes/Default';
import { Decorators } from '@serenity-is/corelib';
import { GoodsReceivedNoteTypesDialog } from './GoodsReceivedNoteTypesDialog';
import { EntityGridDialog, AutoSaveOption } from '@serenity-is/pro.extensions';

@Decorators.registerClass('ReconnBooks.Default.GoodsReceivedNoteTypesGrid')
@Decorators.filterable()
export class GoodsReceivedNoteTypesGrid extends EntityGridDialog<GoodsReceivedNoteTypesRow, any> {
    protected getColumnsKey() { return GoodsReceivedNoteTypesColumns.columnsKey; }
    protected getDialogType() { return GoodsReceivedNoteTypesDialog; }
    protected getRowDefinition() { return GoodsReceivedNoteTypesRow; }
    protected getService() { return GoodsReceivedNoteTypesService.baseUrl; }
    protected autoSaveOnClose() { return AutoSaveOption.Confirm; }
    protected autoSaveOnSwitch() { return AutoSaveOption.Auto; }
}