﻿import { fieldsProxy } from "@serenity-is/corelib";

export interface GoodsReceivedNoteTypesRow {
    RowNumber?: number;
    GoodsReceivedNoteTypeId?: number;
    GoodsReceivedNoteTypeName?: string;
    Description?: string;
}

export abstract class GoodsReceivedNoteTypesRow {
    static readonly idProperty = 'GoodsReceivedNoteTypeId';
    static readonly nameProperty = 'GoodsReceivedNoteTypeName';
    static readonly localTextPrefix = 'Default.GoodsReceivedNoteTypes';
    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<GoodsReceivedNoteTypesRow>();
}