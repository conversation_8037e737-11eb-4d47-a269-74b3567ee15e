﻿import { StringEditor, DateEditor, ServiceLookupEditor, LookupEditor, BooleanEditor, TextAreaEditor, MultipleImageUploadEditor, DateTimeEditor, PrefixedContext, initFormType } from "@serenity-is/corelib";
import { CustomersDialog } from "../../Default/Customers/CustomersDialog";
import { GoodsReceivedNotesDialog } from "../../Default/GoodsReceivedNotes/GoodsReceivedNotesDialog";
import { InvoicesDialog } from "../../Default/Invoices/InvoicesDialog";
import { SalesReturnDetailsGridEditor } from "../../Default/SalesReturnDetails/SalesReturnDetailsGridEditor";

export interface SalesReturnsForm {
    SalesReturnNo: StringEditor;
    SalesReturnDate: DateEditor;
    CustomerId: ServiceLookupEditor;
    DeliveryNoteId: LookupEditor;
    InvoiceId: ServiceLookupEditor;
    GoodsReceivedNoteId: ServiceLookupEditor;
    PlaceOfSupplyStateName: StringEditor;
    SalesReturnDetailsList: SalesReturnDetailsGridEditor;
    FinancialYearId: LookupEditor;
    AuthorizedStatus: BooleanEditor;
    Remarks: TextAreaEditor;
    UploadFiles: MultipleImageUploadEditor;
    PreparedByUserId: LookupEditor;
    PreparedDate: DateTimeEditor;
    VerifiedByUserId: LookupEditor;
    VerifiedDate: DateTimeEditor;
    AuthorizedByUserId: LookupEditor;
    AuthorizedDate: DateTimeEditor;
    ModifiedByUserId: LookupEditor;
    ModifiedDate: DateEditor;
    CancelledByUserId: LookupEditor;
    CancelledDate: DateEditor;
}

export class SalesReturnsForm extends PrefixedContext {
    static readonly formKey = 'Default.SalesReturns';
    private static init: boolean;

    constructor(prefix: string) {
        super(prefix);

        if (!SalesReturnsForm.init)  {
            SalesReturnsForm.init = true;

            var w0 = StringEditor;
            var w1 = DateEditor;
            var w2 = ServiceLookupEditor;
            var w3 = LookupEditor;
            var w4 = SalesReturnDetailsGridEditor;
            var w5 = BooleanEditor;
            var w6 = TextAreaEditor;
            var w7 = MultipleImageUploadEditor;
            var w8 = DateTimeEditor;

            initFormType(SalesReturnsForm, [
                'SalesReturnNo', w0,
                'SalesReturnDate', w1,
                'CustomerId', w2,
                'DeliveryNoteId', w3,
                'InvoiceId', w2,
                'GoodsReceivedNoteId', w2,
                'PlaceOfSupplyStateName', w0,
                'SalesReturnDetailsList', w4,
                'FinancialYearId', w3,
                'AuthorizedStatus', w5,
                'Remarks', w6,
                'UploadFiles', w7,
                'PreparedByUserId', w3,
                'PreparedDate', w8,
                'VerifiedByUserId', w3,
                'VerifiedDate', w8,
                'AuthorizedByUserId', w3,
                'AuthorizedDate', w8,
                'ModifiedByUserId', w3,
                'ModifiedDate', w1,
                'CancelledByUserId', w3,
                'CancelledDate', w1
            ]);
        }
    }
}

queueMicrotask(() => [CustomersDialog, InvoicesDialog, GoodsReceivedNotesDialog]); // referenced dialogs