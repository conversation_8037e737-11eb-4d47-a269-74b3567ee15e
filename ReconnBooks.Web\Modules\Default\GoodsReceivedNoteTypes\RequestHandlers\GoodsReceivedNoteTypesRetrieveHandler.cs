﻿using Serenity.Services;
using MyRequest = Serenity.Services.RetrieveRequest;
using MyResponse = Serenity.Services.RetrieveResponse<ReconnBooks.Default.GoodsReceivedNoteTypesRow>;
using MyRow = ReconnBooks.Default.GoodsReceivedNoteTypesRow;

namespace ReconnBooks.Default;

public interface IGoodsReceivedNoteTypesRetrieveHandler : IRetrieveHandler<MyRow, MyRequest, MyResponse> {}

public class GoodsReceivedNoteTypesRetrieveHandler : RetrieveRequestHandler<MyRow, MyRequest, MyResponse>, IGoodsReceivedNoteTypesRetrieveHandler
{
    public GoodsReceivedNoteTypesRetrieveHandler(IRequestContext context)
            : base(context)
    {
    }
}