﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { GoodsReceivedNoteTypesRow } from "./GoodsReceivedNoteTypesRow";

export namespace GoodsReceivedNoteTypesService {
    export const baseUrl = 'Default/GoodsReceivedNoteTypes';

    export declare function Create(request: SaveRequest<GoodsReceivedNoteTypesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<GoodsReceivedNoteTypesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<GoodsReceivedNoteTypesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<GoodsReceivedNoteTypesRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<GoodsReceivedNoteTypesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<GoodsReceivedNoteTypesRow>>;

    export const Methods = {
        Create: "Default/GoodsReceivedNoteTypes/Create",
        Update: "Default/GoodsReceivedNoteTypes/Update",
        Delete: "Default/GoodsReceivedNoteTypes/Delete",
        Retrieve: "Default/GoodsReceivedNoteTypes/Retrieve",
        List: "Default/GoodsReceivedNoteTypes/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>GoodsReceivedNoteTypesService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}