using Serenity.Services;
using MyRequest = Serenity.Services.DeleteRequest;
using MyResponse = Serenity.Services.DeleteResponse;
using MyRow = ReconnBooks.Default.GoodsReceivedNotesRow;

namespace ReconnBooks.Default;

public interface IGoodsReceivedNotesDeleteHandler : <PERSON>eleteHandler<MyRow, MyRequest, MyResponse> {}

public class GoodsReceivedNotesDeleteHandler : DeleteRequestHandler<MyRow, MyRequest, MyResponse>, IGoodsReceivedNotesDeleteHandler
{
    public GoodsReceivedNotesDeleteHandler(IRequestContext context)
            : base(context)
    {
    }
}