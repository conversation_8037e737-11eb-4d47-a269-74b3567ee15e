using ReconnBooks.Common.RowBehaviors;
using ReconnBooks.Modules.Common.Helpers;
using ReconnBooks.Modules.Default.Clients;
using Serenity.ComponentModel;
using Serenity.Data;
using Serenity.Data.Mapping;
using System;
using System.ComponentModel;

namespace ReconnBooks.Default;

[<PERSON><PERSON><PERSON>("Default"), <PERSON><PERSON><PERSON>("Default"), TableName("SalesReturns")]
[Di<PERSON><PERSON><PERSON><PERSON>("Sales Returns"), InstanceName("Sales Returns"), GenerateFields]
[ReadPermission("Administration:General")]
[ModifyPermission("Administration:General")]
[ServiceLookupPermission("Administration:General")]
[LookupScript]
public sealed partial class SalesReturnsRow : Row<SalesReturnsRow.RowFields>, IIdRow, INameRow, IMultiClientRow, IRowNumberedRow //to add MultiTanancy
{
    const string jCustomer = nameof(jCustomer);
    const string jDeliveryNote = nameof(jDeliveryNote);
    const string jInvoice = nameof(jInvoice);
    const string jFinancialYear = nameof(jFinancialYear);
    const string jPreparedByUser = nameof(jPreparedByUser);
    const string jClient = nameof(jClient);
    const string jVerifiedByUser = nameof(jVerifiedByUser);
    const string jAuthorizedByUser = nameof(jAuthorizedByUser);
    const string jModifiedByUser = nameof(jModifiedByUser);
    const string jCancelledByUser = nameof(jCancelledByUser);
    const string jGoodsReceivedNote = nameof(jGoodsReceivedNote);

    [DisplayName("Sl.No."), NotMapped, Width(50), AlignCenter] // RowNumbering
    public long? RowNumber { get => fields.RowNumber[this]; set => fields.RowNumber[this] = value; }
    public Int64Field RowNumberField { get => fields.RowNumber; }

    [DisplayName("Sales Return Id"), Identity, IdProperty]
    public int? SalesReturnId { get => fields.SalesReturnId[this]; set => fields.SalesReturnId[this] = value; }

    [DisplayName("Sales Return No."), Size(50), NotNull, QuickSearch, NameProperty, Unique]
    public string SalesReturnNo { get => fields.SalesReturnNo[this]; set => fields.SalesReturnNo[this] = value; }

    [DisplayName("Sales Return Date")]
    public DateTime? SalesReturnDate { get => fields.SalesReturnDate[this]; set => fields.SalesReturnDate[this] = value; }

    [DisplayName("Month"), Expression("FORMAT(SalesReturnDate, 'MMM')"), QuickFilter]
    [LookupEditor(typeof(MonthLookup))]
    public string SalesReturnMonth { get => fields.SalesReturnMonth[this]; set => fields.SalesReturnMonth[this] = value; }

    [DisplayName("Customer Name"), NotNull, ForeignKey(typeof(CustomersRow)), LeftJoin(jCustomer), TextualField(nameof(CustomerCompanyName))]
    [ServiceLookupEditor(typeof(CustomersRow), InplaceAdd = true, Service = "Default/Customers/List")]
    public int? CustomerId { get => fields.CustomerId[this]; set => fields.CustomerId[this] = value; }

    // ----------- Fetching Billing Address -----------------
    [DisplayName("Billing Address")]
    [Origin(jCustomer, nameof(CustomersRow.BillingAddress)), LookupInclude]
    public string BillingAddress { get => fields.BillingAddress[this]; set => fields.BillingAddress[this] = value; }

    [DisplayName("City")]
    [Origin(jCustomer, nameof(CustomersRow.BillingCityCityName)), LookupInclude]
    public string BillingCityCityName { get => fields.BillingCityCityName[this]; set => fields.BillingCityCityName[this] = value; }

    [DisplayName("PIN Code")]
    [Origin(jCustomer, nameof(CustomersRow.BillingPinCode)), LookupInclude]
    public string BillingPinCode { get => fields.BillingPinCode[this]; set => fields.BillingPinCode[this] = value; }

    [DisplayName("GST No.")]
    [Origin(jCustomer, nameof(CustomersRow.GSTIN)), LookupInclude]
    public string GSTIN { get => fields.GSTIN[this]; set => fields.GSTIN[this] = value; }

    [DisplayName("Place of Supply")]
    [Origin(jCustomer, nameof(CustomersRow.PlaceOfSupplyStateName)), LookupInclude]
    public string PlaceOfSupplyStateName { get => fields.PlaceOfSupplyStateName[this]; set => fields.PlaceOfSupplyStateName[this] = value; }
    /// <summary>
    //-----------------------------------------------------------------------------

    [DisplayName("Delivery Note No."), ForeignKey(typeof(DeliveryNotesRow)), LeftJoin(jDeliveryNote), TextualField(nameof(DeliveryNoteNo))]
    [LookupEditor(typeof(DeliveryNotesRow), Async = true)]
    public int? DeliveryNoteId { get => fields.DeliveryNoteId[this]; set => fields.DeliveryNoteId[this] = value; }

    [DisplayName("Invoice No."), ForeignKey(typeof(InvoicesRow)), LeftJoin(jInvoice), TextualField(nameof(InvoiceNo))]
    [ServiceLookupEditor(typeof(InvoicesRow), InplaceAdd = true, Service = "Default/Invoices/List",
       CascadeFrom = nameof(CustomerId), CascadeValue = nameof(CustomerId))]
    public int? InvoiceId { get => fields.InvoiceId[this]; set => fields.InvoiceId[this] = value; }

    [DisplayName("GoodsReceivedNote No."), ForeignKey(typeof(GoodsReceivedNotesRow)), LeftJoin(jGoodsReceivedNote), TextualField(nameof(GoodsReceivedNoteNo))]
    [ServiceLookupEditor(typeof(GoodsReceivedNotesRow), InplaceAdd = true, Service = "Default/GoodsReceivedNotes/List")]
    public int? GoodsReceivedNoteId { get => fields.GoodsReceivedNoteId[this]; set => fields.GoodsReceivedNoteId[this] = value; }

    [DisplayName("GoodsReceivedNote No."), Origin(jGoodsReceivedNote, nameof(GoodsReceivedNotesRow.GoodsReceivedNoteNo))]
    public string GoodsReceivedNoteNo { get => fields.GoodsReceivedNoteNo[this]; set => fields.GoodsReceivedNoteNo[this] = value; }

    [DisplayName("Financial Year"), ForeignKey(typeof(FinancialYearsRow)), LeftJoin(jFinancialYear)]
    [TextualField(nameof(FinancialYearName))]
    [LookupEditor(typeof(FinancialYearsRow))]
    public int? FinancialYearId { get => fields.FinancialYearId[this]; set => fields.FinancialYearId[this] = value; }
    
    //-------------------------------------------------------------------------
    [MasterDetailRelation(foreignKey: nameof(SalesReturnDetailsRow.SalesReturnId)), NotMapped]
    public List<SalesReturnDetailsRow> SalesReturnDetailsList
    {
        get { return Fields.SalesReturnDetailsList[this]; }
        set { Fields.SalesReturnDetailsList[this] = value; }
    }
    //-------------------------------------------------------------------------

    [DisplayName("Attach Files"), Size(500)]
    public string UploadFiles { get => fields.UploadFiles[this]; set => fields.UploadFiles[this] = value; }

    [DisplayName("Remarks")]
    public string Remarks { get => fields.Remarks[this]; set => fields.Remarks[this] = value; }

    [DisplayName("Client"), NotNull, ForeignKey(typeof(ClientsRow)), LeftJoin(jClient), TextualField(nameof(ClientName))]
    [ServiceLookupEditor(typeof(ClientsRow), Service = "Default/Clients/List")]
    [Insertable(false), Updatable(false)] //add for MultiTenancy 
    public int? ClientId { get => fields.ClientId[this]; set => fields.ClientId[this] = value; }

    [DisplayName("Client"), Origin(jClient, nameof(ClientsRow.ClientName))]
    public string ClientName { get => fields.ClientName[this]; set => fields.ClientName[this] = value; }

    [DisplayName("Prepared By"), ForeignKey(typeof(Administration.UserRow)), LeftJoin(jPreparedByUser)]
    [TextualField(nameof(PreparedByUserUsername)), LookupEditor(typeof(Administration.UserRow), Async = true)]
    public int? PreparedByUserId { get => fields.PreparedByUserId[this]; set => fields.PreparedByUserId[this] = value; }

    [DisplayName("Prepared Date"), DateTimeEditor]
    public DateTime? PreparedDate { get => fields.PreparedDate[this]; set => fields.PreparedDate[this] = value; }

    [DisplayName("Verified By"), ForeignKey(typeof(Administration.UserRow)), LeftJoin(jVerifiedByUser)]
    [TextualField(nameof(VerifiedByUserUsername)), LookupEditor(typeof(Administration.UserRow), Async = true)]
    public int? VerifiedByUserId { get => fields.VerifiedByUserId[this]; set => fields.VerifiedByUserId[this] = value; }

    [DisplayName("Verified Date"), DateTimeEditor]
    public DateTime? VerifiedDate { get => fields.VerifiedDate[this]; set => fields.VerifiedDate[this] = value; }

    [DisplayName("Authorized By"), ForeignKey(typeof(Administration.UserRow)), LeftJoin(jAuthorizedByUser)]
    [TextualField(nameof(AuthorizedByUserUsername)), LookupEditor(typeof(Administration.UserRow), Async = true)]
    public int? AuthorizedByUserId { get => fields.AuthorizedByUserId[this]; set => fields.AuthorizedByUserId[this] = value; }

    [DisplayName("Authorized Date"), DateTimeEditor]
    public DateTime? AuthorizedDate { get => fields.AuthorizedDate[this]; set => fields.AuthorizedDate[this] = value; }

    [DisplayName("Modified By"), ForeignKey(typeof(Administration.UserRow)), LeftJoin(jModifiedByUser)]
    [TextualField(nameof(ModifiedByUserUsername)), LookupEditor(typeof(Administration.UserRow), Async = true)]
    public int? ModifiedByUserId { get => fields.ModifiedByUserId[this]; set => fields.ModifiedByUserId[this] = value; }

    [DisplayName("Modified Date")]
    public DateTime? ModifiedDate { get => fields.ModifiedDate[this]; set => fields.ModifiedDate[this] = value; }

    [DisplayName("Cancelled By"), ForeignKey(typeof(Administration.UserRow)), LeftJoin(jCancelledByUser)]
    [TextualField(nameof(CancelledByUserUsername)), LookupEditor(typeof(Administration.UserRow), Async = true)]
    public int? CancelledByUserId { get => fields.CancelledByUserId[this]; set => fields.CancelledByUserId[this] = value; }

    [DisplayName("Cancelled Date")]
    public DateTime? CancelledDate { get => fields.CancelledDate[this]; set => fields.CancelledDate[this] = value; }

    [DisplayName("Authorization Status"), NotNull]
    public bool? AuthorizedStatus { get => fields.AuthorizedStatus[this]; set => fields.AuthorizedStatus[this] = value; }

    [DisplayName("Customer Name"), Origin(jCustomer, nameof(CustomersRow.CompanyName)), QuickSearch]
    public string CustomerCompanyName { get => fields.CustomerCompanyName[this]; set => fields.CustomerCompanyName[this] = value; }

    [DisplayName("Delivery Note No."), Origin(jDeliveryNote, nameof(DeliveryNotesRow.DeliveryNoteNo))]
    public string DeliveryNoteNo { get => fields.DeliveryNoteNo[this]; set => fields.DeliveryNoteNo[this] = value; }

    [DisplayName("Invoice No."), Origin(jInvoice, nameof(InvoicesRow.InvoiceNo))]
    public string InvoiceNo { get => fields.InvoiceNo[this]; set => fields.InvoiceNo[this] = value; }

    [DisplayName("Financial Year"), Origin(jFinancialYear, nameof(FinancialYearsRow.FinancialYearName))]
    public string FinancialYearName { get => fields.FinancialYearName[this]; set => fields.FinancialYearName[this] = value; }

    [DisplayName("Prepared By"), Origin(jPreparedByUser, nameof(Administration.UserRow.Username))]
    public string PreparedByUserUsername { get => fields.PreparedByUserUsername[this]; set => fields.PreparedByUserUsername[this] = value; }

    [DisplayName("Verified By"), Origin(jVerifiedByUser, nameof(Administration.UserRow.Username))]
    public string VerifiedByUserUsername { get => fields.VerifiedByUserUsername[this]; set => fields.VerifiedByUserUsername[this] = value; }

    [DisplayName("Authorized By"), Origin(jAuthorizedByUser, nameof(Administration.UserRow.Username))]
    public string AuthorizedByUserUsername { get => fields.AuthorizedByUserUsername[this]; set => fields.AuthorizedByUserUsername[this] = value; }

    [DisplayName("Modified By"), Origin(jModifiedByUser, nameof(Administration.UserRow.Username))]
    public string ModifiedByUserUsername { get => fields.ModifiedByUserUsername[this]; set => fields.ModifiedByUserUsername[this] = value; }

    [DisplayName("Cancelled By"), Origin(jCancelledByUser, nameof(Administration.UserRow.Username))]
    public string CancelledByUserUsername { get => fields.CancelledByUserUsername[this]; set => fields.CancelledByUserUsername[this] = value; }
    public Int32Field ClientIdField => fields.ClientId; //add for MultiTenancy 
}