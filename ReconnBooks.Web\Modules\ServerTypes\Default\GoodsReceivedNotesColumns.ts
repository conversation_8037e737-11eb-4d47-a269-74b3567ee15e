﻿import { ColumnsBase, fieldsProxy } from "@serenity-is/corelib";
import { Column } from "@serenity-is/sleekgrid";
import { GoodsReceivedNotesRow } from "./GoodsReceivedNotesRow";

export interface GoodsReceivedNotesColumns {
    RowNumber: Column<GoodsReceivedNotesRow>;
    GoodsReceivedNoteNo: Column<GoodsReceivedNotesRow>;
    GoodsReceivedNoteDate: Column<GoodsReceivedNotesRow>;
    GoodsReceivedNoteTypeName: Column<GoodsReceivedNotesRow>;
    VendorName: Column<GoodsReceivedNotesRow>;
    PurchaseOrderNo: Column<GoodsReceivedNotesRow>;
    VendorDcInvoiceNo: Column<GoodsReceivedNotesRow>;
    VendorDcInvoiceDate: Column<GoodsReceivedNotesRow>;
    ShippingDocketNo: Column<GoodsReceivedNotesRow>;
    ShippedThrough: Column<GoodsReceivedNotesRow>;
    DeliveryAddress: Column<GoodsReceivedNotesRow>;
    DeliveryCityCityName: Column<GoodsReceivedNotesRow>;
    VehicleNo: Column<GoodsReceivedNotesRow>;
    VehicleType: Column<GoodsReceivedNotesRow>;
    GatePassNo: Column<GoodsReceivedNotesRow>;
    GatePassDate: Column<GoodsReceivedNotesRow>;
    Inspection: Column<GoodsReceivedNotesRow>;
    ReceivedByEmployeeEmployeeName: Column<GoodsReceivedNotesRow>;
    FinancialYearName: Column<GoodsReceivedNotesRow>;
    GoodsReceivedNoteMonth: Column<GoodsReceivedNotesRow>;
    UploadDocuments: Column<GoodsReceivedNotesRow>;
    Remarks: Column<GoodsReceivedNotesRow>;
    ClientId: Column<GoodsReceivedNotesRow>;
    PreparedByUserUsername: Column<GoodsReceivedNotesRow>;
    PreparedDate: Column<GoodsReceivedNotesRow>;
    VerifiedByUserUsername: Column<GoodsReceivedNotesRow>;
    VerifiedDate: Column<GoodsReceivedNotesRow>;
    AuthorizedByUserUsername: Column<GoodsReceivedNotesRow>;
    AuthorizedDate: Column<GoodsReceivedNotesRow>;
    ModifiedByUserUsername: Column<GoodsReceivedNotesRow>;
    ModifiedDate: Column<GoodsReceivedNotesRow>;
    CancelledByUserUsername: Column<GoodsReceivedNotesRow>;
    CancelledDate: Column<GoodsReceivedNotesRow>;
    AuthorizedStatus: Column<GoodsReceivedNotesRow>;
    GoodsReceivedNoteId: Column<GoodsReceivedNotesRow>;
}

export class GoodsReceivedNotesColumns extends ColumnsBase<GoodsReceivedNotesRow> {
    static readonly columnsKey = 'Default.GoodsReceivedNotes';
    static readonly Fields = fieldsProxy<GoodsReceivedNotesColumns>();
}