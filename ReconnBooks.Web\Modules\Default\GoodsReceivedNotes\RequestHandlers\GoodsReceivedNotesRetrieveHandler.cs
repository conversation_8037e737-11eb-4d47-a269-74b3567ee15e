using Serenity.Services;
using MyRequest = Serenity.Services.RetrieveRequest;
using MyResponse = Serenity.Services.RetrieveResponse<ReconnBooks.Default.GoodsReceivedNotesRow>;
using MyRow = ReconnBooks.Default.GoodsReceivedNotesRow;

namespace ReconnBooks.Default;

public interface IGoodsReceivedNotesRetrieveHandler : IRetrieveHandler<MyRow, MyRequest, MyResponse> {}

public class GoodsReceivedNotesRetrieveHandler : RetrieveRequestHandler<MyRow, MyRequest, MyResponse>, IGoodsReceivedNotesRetrieveHandler
{
    public GoodsReceivedNotesRetrieveHandler(IRequestContext context)
            : base(context)
    {
    }
}