﻿using Serenity.Services;
using MyRequest = Serenity.Services.SaveRequest<ReconnBooks.Default.GoodsReceivedNoteDetailsRow>;
using MyResponse = Serenity.Services.SaveResponse;
using MyRow = ReconnBooks.Default.GoodsReceivedNoteDetailsRow;

namespace ReconnBooks.Default;

public interface IGoodsReceivedNoteDetailsSaveHandler : ISaveHandler<MyRow, MyRequest, MyResponse> {}

public class GoodsReceivedNoteDetailsSaveHandler : SaveRequestHandler<MyRow, MyRequest, MyResponse>, IGoodsReceivedNoteDetailsSaveHandler
{
    public GoodsReceivedNoteDetailsSaveHandler(IRequestContext context)
            : base(context)
    {
    }
}