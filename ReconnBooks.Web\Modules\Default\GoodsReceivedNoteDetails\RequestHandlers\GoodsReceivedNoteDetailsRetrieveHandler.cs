﻿using Serenity.Services;
using MyRequest = Serenity.Services.RetrieveRequest;
using MyResponse = Serenity.Services.RetrieveResponse<ReconnBooks.Default.GoodsReceivedNoteDetailsRow>;
using MyRow = ReconnBooks.Default.GoodsReceivedNoteDetailsRow;

namespace ReconnBooks.Default;

public interface IGoodsReceivedNoteDetailsRetrieveHandler : IRetrieveHandler<MyRow, MyRequest, MyResponse> {}

public class GoodsReceivedNoteDetailsRetrieveHandler : RetrieveRequestHandler<MyRow, MyRequest, MyResponse>, IGoodsReceivedNoteDetailsRetrieveHandler
{
    public GoodsReceivedNoteDetailsRetrieveHandler(IRequestContext context)
            : base(context)
    {
    }
}