﻿using Serenity.Services;
using MyRequest = Serenity.Services.DeleteRequest;
using MyResponse = Serenity.Services.DeleteResponse;
using MyRow = ReconnBooks.Default.GoodsReceivedNoteDetailsRow;

namespace ReconnBooks.Default;

public interface IGoodsReceivedNoteDetailsDeleteHandler : IDeleteHandler<MyRow, MyRequest, MyResponse> {}

public class GoodsReceivedNoteDetailsDeleteHandler : DeleteRequestHandler<MyRow, MyRequest, MyResponse>, IGoodsReceivedNoteDetailsDeleteHandler
{
    public GoodsReceivedNoteDetailsDeleteHandler(IRequestContext context)
            : base(context)
    {
    }
}