import { GoodsReceivedNotesForm, GoodsReceivedNotesRow, GoodsReceivedNotesService, VendorsRow, CitiesRow, DocumentsRow} from '@/ServerTypes/Default';
import { Decorators, toId, getRemoteData, alertDialog, WidgetProps } from '@serenity-is/corelib';
import { FinancialYearHelper } from '../FinancialYears/FinancialYearHelper';
import { VerifyAuthorizeDialog } from '../../Common/Helpers/VerifyAuthorizeDialog';
import { ReportHelper } from '@serenity-is/extensions';

@Decorators.registerClass('ReconnBooks.Default.GoodsReceivedNotesDialog')
@Decorators.panel()
export class GoodsReceivedNotesDialog extends VerifyAuthorizeDialog<GoodsReceivedNotesRow> {
    protected getFormKey() { return GoodsReceivedNotesForm.formKey; }
    protected getRowDefinition() { return GoodsReceivedNotesRow; }
    protected getService() { return GoodsReceivedNotesService.baseUrl; }

    protected form = new GoodsReceivedNotesForm(this.idPrefix);
    private docType: string;

    constructor(props: WidgetProps<any>) {
        super(props);

        (this.form.GoodsReceivedNoteDetailsList.view as any).onRowsOrCountChanged.subscribe((e) => {
            e.stopPropagation();
            this.form.GoodsReceivedNoteDetailsList.getGrid().focus();

            const grid = this.form.GoodsReceivedNoteDetailsList.getGrid();
            const rowCount = grid.getDataLength();
            if (rowCount > 0) {
                grid.scrollRowIntoView(rowCount - 1);
            }
        });

        //--Fetching Customer Billing Address--

        this.form.VendorId.change(a => {
            setTimeout(async () => {
                var VendorId = toId(this.form.VendorId.value);
                if (VendorId != null) {
                    var Vendor = (await VendorsRow.getLookupAsync()).itemById[VendorId];
                    this.form.GSTIN.value = Vendor.GSTIN;
                }
                else {
                    this.clearVendorFields();
                }
            }, 100);
        })

        this.form.PurchaseOrderId.changeSelect2(e => {
            if (this.form.PurchaseOrderId.value === '') {
                // Clear the details in the grid
                this.form.GoodsReceivedNoteDetailsList.value = [];
            }
            else {
                GoodsReceivedNotesService.GetFromPurchaseOrderDetails({
                    EntityId: toId(this.form.PurchaseOrderId.value)
                },
                    response => {
                        this.form.GoodsReceivedNoteDetailsList.value = response.Entities;
                    });
            }
        });
        //--Financial Year--

        this.form.FinancialYearId.changeSelect2(e => {
            this.getNextNumber();
        });
    }

    protected async afterLoadEntity() {
        super.afterLoadEntity();
        if (this.isNew()) {

            if (!this.form.FinancialYearId?.value) {
                FinancialYearHelper.getCurrentFinancialYearId().then(currentFinancialYearId => {
                    this.form.FinancialYearId.value = currentFinancialYearId.toString();
                    this.getNextNumber();
                    this.setDialogsLoadedState();
                });
            }
            else {
                this.getNextNumber();
                this.setDialogsLoadedState();
            }
        }
        this.setDialogsLoadedState();
    }

    //--Cloning a Document (Save As)--
    protected updateInterface() {
        super.updateInterface();
        this.cloneButton.toggle(this.isEditMode());
    }

    protected getCloningEntity() {
        var clonedEntity = super.getCloningEntity();
        return clonedEntity;
    }

    //--Document Number Generation--
    private getNextNumber(): any {
        
        if (this.docType == null) {
            this.docType = DocumentsRow.getLookup().items.filter(a => a.DocumentName == "GoodsReceivedNotes")[0].DocumentShortName;
        }
        var prefix = this.getNextNumberPrefix(this.docType, this.form.FinancialYearId.text);

        this.form.GoodsReceivedNoteNo.value = prefix;
    }

    protected validateBeforeSave() {
        if (!this.form.GoodsReceivedNoteDetailsList.value || this.form.GoodsReceivedNoteDetailsList.value.length === 0) {
            alertDialog(" GoodsReceivedNotes cannot be saved because no items have been added. Please add at least one item to proceed.");
            return false;
        }
        return true;
    }
    getToolbarButtons() {
        var buttons = super.getToolbarButtons();

        buttons.push({
            title: 'Print',
            icon: 'fas fa-file-pdf text-danger',
            cssClass: 'print-button',

            onClick: () => {
                ReportHelper.execute({
                    reportKey: 'GoodsReceivedNoteReport',
                    params: {
                        ID: this.entityId
                    }
                });
            }
        });
        return buttons;
    }
    private clearVendorFields() {
        this.form.GSTIN.value = undefined;
        this.form.GoodsReceivedNoteDetailsList.value = undefined;

    }
}