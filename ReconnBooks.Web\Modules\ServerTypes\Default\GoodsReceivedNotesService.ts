﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, ServiceResponse, serviceRequest } from "@serenity-is/corelib";
import { GetNextNumberRequest, GetNextNumberResponse } from "@serenity-is/extensions";
import { EmailRequest } from "../Modules/Common.Helpers.EmailHelper.EmailRequest";
import { GoodsReceivedNoteDetailsRow } from "./GoodsReceivedNoteDetailsRow";
import { GoodsReceivedNotesRow } from "./GoodsReceivedNotesRow";

export namespace GoodsReceivedNotesService {
    export const baseUrl = 'Default/GoodsReceivedNotes';

    export declare function Create(request: SaveRequest<GoodsReceivedNotesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<GoodsReceivedNotesRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<GoodsReceivedNotesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<GoodsReceivedNotesRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<GoodsReceivedNotesRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<GoodsReceivedNotesRow>>;
    export declare function GetNextNumber(request: GetNextNumberRequest, onSuccess?: (response: GetNextNumberResponse) => void, opt?: ServiceOptions<any>): PromiseLike<GetNextNumberResponse>;
    export declare function GetFromPurchaseOrderDetails(request: RetrieveRequest, onSuccess?: (response: ListResponse<GoodsReceivedNoteDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<GoodsReceivedNoteDetailsRow>>;
    export declare function EmailGoodsReceivedNote(request: EmailRequest, onSuccess?: (response: ServiceResponse) => void, opt?: ServiceOptions<any>): PromiseLike<ServiceResponse>;

    export const Methods = {
        Create: "Default/GoodsReceivedNotes/Create",
        Update: "Default/GoodsReceivedNotes/Update",
        Delete: "Default/GoodsReceivedNotes/Delete",
        Retrieve: "Default/GoodsReceivedNotes/Retrieve",
        List: "Default/GoodsReceivedNotes/List",
        GetNextNumber: "Default/GoodsReceivedNotes/GetNextNumber",
        GetFromPurchaseOrderDetails: "Default/GoodsReceivedNotes/GetFromPurchaseOrderDetails",
        EmailGoodsReceivedNote: "Default/GoodsReceivedNotes/EmailGoodsReceivedNote"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List', 
        'GetNextNumber', 
        'GetFromPurchaseOrderDetails', 
        'EmailGoodsReceivedNote'
    ].forEach(x => {
        (<any>GoodsReceivedNotesService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}