using Serenity.Services;
using MyRequest = Serenity.Services.SaveRequest<ReconnBooks.Default.GoodsReceivedNotesRow>;
using MyResponse = Serenity.Services.SaveResponse;
using MyRow = ReconnBooks.Default.GoodsReceivedNotesRow;

namespace ReconnBooks.Default;

public interface IGoodsReceivedNotesSaveHandler : ISaveHandler<MyRow, MyRequest, MyResponse> {}

public class GoodsReceivedNotesSaveHandler : SaveRequestHandler<MyRow, MyRequest, MyResponse>, IGoodsReceivedNotesSaveHandler
{
    public GoodsReceivedNotesSaveHandler(IRequestContext context)
            : base(context)
    {
    }
}