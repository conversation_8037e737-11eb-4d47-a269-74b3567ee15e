﻿import { SaveRequest, SaveResponse, ServiceOptions, DeleteRequest, DeleteResponse, RetrieveRequest, RetrieveResponse, ListRequest, ListResponse, serviceRequest } from "@serenity-is/corelib";
import { GoodsReceivedNoteDetailsRow } from "./GoodsReceivedNoteDetailsRow";

export namespace GoodsReceivedNoteDetailsService {
    export const baseUrl = 'Default/GoodsReceivedNoteDetails';

    export declare function Create(request: SaveRequest<GoodsReceivedNoteDetailsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Update(request: SaveRequest<GoodsReceivedNoteDetailsRow>, onSuccess?: (response: SaveResponse) => void, opt?: ServiceOptions<any>): PromiseLike<SaveResponse>;
    export declare function Delete(request: DeleteRequest, onSuccess?: (response: DeleteResponse) => void, opt?: ServiceOptions<any>): PromiseLike<DeleteResponse>;
    export declare function Retrieve(request: RetrieveRequest, onSuccess?: (response: RetrieveResponse<GoodsReceivedNoteDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<RetrieveResponse<GoodsReceivedNoteDetailsRow>>;
    export declare function List(request: ListRequest, onSuccess?: (response: ListResponse<GoodsReceivedNoteDetailsRow>) => void, opt?: ServiceOptions<any>): PromiseLike<ListResponse<GoodsReceivedNoteDetailsRow>>;

    export const Methods = {
        Create: "Default/GoodsReceivedNoteDetails/Create",
        Update: "Default/GoodsReceivedNoteDetails/Update",
        Delete: "Default/GoodsReceivedNoteDetails/Delete",
        Retrieve: "Default/GoodsReceivedNoteDetails/Retrieve",
        List: "Default/GoodsReceivedNoteDetails/List"
    } as const;

    [
        'Create', 
        'Update', 
        'Delete', 
        'Retrieve', 
        'List'
    ].forEach(x => {
        (<any>GoodsReceivedNoteDetailsService)[x] = function (r, s, o) {
            return serviceRequest(baseUrl + '/' + x, r, s, o);
        };
    });
}