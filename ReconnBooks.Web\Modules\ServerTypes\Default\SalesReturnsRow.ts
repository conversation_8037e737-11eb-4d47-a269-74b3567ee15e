﻿import { getLookup, getLookupAsync, fieldsProxy } from "@serenity-is/corelib";
import { SalesReturnDetailsRow } from "./SalesReturnDetailsRow";

export interface SalesReturnsRow {
    RowNumber?: number;
    SalesReturnId?: number;
    SalesReturnNo?: string;
    SalesReturnDate?: string;
    SalesReturnMonth?: string;
    CustomerId?: number;
    BillingAddress?: string;
    BillingCityCityName?: string;
    BillingPinCode?: string;
    GSTIN?: string;
    PlaceOfSupplyStateName?: string;
    DeliveryNoteId?: number;
    InvoiceId?: number;
    GoodsReceivedNoteId?: number;
    GoodsReceivedNoteNo?: string;
    FinancialYearId?: number;
    SalesReturnDetailsList?: SalesReturnDetailsRow[];
    UploadFiles?: string;
    Remarks?: string;
    ClientId?: number;
    ClientName?: string;
    PreparedByUserId?: number;
    PreparedDate?: string;
    VerifiedByUserId?: number;
    VerifiedDate?: string;
    AuthorizedByUserId?: number;
    AuthorizedDate?: string;
    ModifiedByUserId?: number;
    ModifiedDate?: string;
    CancelledByUserId?: number;
    CancelledDate?: string;
    AuthorizedStatus?: boolean;
    CustomerCompanyName?: string;
    DeliveryNoteNo?: string;
    InvoiceNo?: string;
    FinancialYearName?: string;
    PreparedByUserUsername?: string;
    VerifiedByUserUsername?: string;
    AuthorizedByUserUsername?: string;
    ModifiedByUserUsername?: string;
    CancelledByUserUsername?: string;
}

export abstract class SalesReturnsRow {
    static readonly idProperty = 'SalesReturnId';
    static readonly nameProperty = 'SalesReturnNo';
    static readonly localTextPrefix = 'Default.SalesReturns';
    static readonly lookupKey = 'Default.SalesReturns';

    /** @deprecated use getLookupAsync instead */
    static getLookup() { return getLookup<SalesReturnsRow>('Default.SalesReturns') }
    static async getLookupAsync() { return getLookupAsync<SalesReturnsRow>('Default.SalesReturns') }

    static readonly deletePermission = 'Administration:General';
    static readonly insertPermission = 'Administration:General';
    static readonly readPermission = 'Administration:General';
    static readonly updatePermission = 'Administration:General';

    static readonly Fields = fieldsProxy<SalesReturnsRow>();
}