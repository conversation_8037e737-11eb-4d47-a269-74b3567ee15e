﻿CREATE TABLE [dbo].[GoodsReceivedNoteDetails]
(
    [GoodsReceivedNoteDetailId]           INT             NOT NULL    IDENTITY (1, 1),
    [GoodsReceivedNoteId]                 INT             NOT NULL,
    [PurchaseOrderDetailId] INT                 NULL,   --cascade Purchase Order Items from Selected POs in GoodsReceivedNotes form,  
    [CommodityTypeId]       INT             NOT NULL,
    
    [CommodityId]           BIGINT			    NULL,
	[CommodityDescription]  NVARCHAR (MAX)      NULL,

    [POQuantity]		    DECIMAL (18,2)	    NULL	CONSTRAINT [DF_PODetails_Quantity] DEFAULT ((1)),   --POAmended Qty from Purchase Order Details table
    [POUnitId]	            INT				    NULL,   --these two fields data not required to save in this table. still i am saving 
    [ReceivedQuantity]		DECIMAL (18,2)	NOT NULL	CONSTRAINT [DF_GoodsReceivedNoteDetails_Quantity] DEFAULT ((1)),  
    [ReceivedUnitId]	    INT				NOT NULL,
    [SKU]		            NVARCHAR (300)		NULL,
    [SerialNos]		        NVARCHAR (MAX)		NULL,	--Serial No of an Item can be entered here. This Sl.No may be scanned by BarCode reader. Multiple Sl.Nos are possible

    [AcceptedQuantity]	    DECIMAL (18,2)	    NULL,   --This information need to be updated from Rejection Note. else value of Accepted Qty.
    [AcceptedUnitId]	    INT				    NULL,   

    [SupplyDueDate]         DATETIME            NULL,
    [LocationId]            INT                 NULL,
    [WarehouseId]           INT                 NULL,
    [StoreId]               INT                 NULL,
    [RackId]                INT                 NULL,
    [Remarks]               NVARCHAR (MAX)      NULL, 
    [ClientId]				INT				NOT NULL    CONSTRAINT [DF_GoodsReceivedNoteDetails_ClientId]	DEFAULT ((0)),

    CONSTRAINT [PK_GoodsReceivedNoteDetails] PRIMARY KEY CLUSTERED    ([GoodsReceivedNoteDetailId] ASC),
    CONSTRAINT [FK_GoodsReceivedNoteDetails_GoodsReceivedNotes]             FOREIGN KEY ([GoodsReceivedNoteId])	                REFERENCES [dbo].[GoodsReceivedNotes] ([GoodsReceivedNoteId]),
    CONSTRAINT [FK_GoodsReceivedNoteDetails_PODetails]        FOREIGN KEY ([PurchaseOrderDetailId])   REFERENCES [dbo].[PurchaseOrderDetails] ([PurchaseOrderDetailId]),
    CONSTRAINT [FK_GoodsReceivedNoteDetails_CommodityTypes]   FOREIGN KEY ([CommodityTypeId])	        REFERENCES [dbo].[CommodityTypes] ([CommodityTypeId]),
    CONSTRAINT [FK_GoodsReceivedNoteDetails_Commodities]      FOREIGN KEY ([CommodityId])		        REFERENCES [dbo].[Commodities] ([CommodityId]),

    CONSTRAINT [FK_GoodsReceivedNoteDetails_POUnits]	        FOREIGN KEY ([POUnitId])        REFERENCES [dbo].[Units] ([UnitId]),
    CONSTRAINT [FK_GoodsReceivedNoteDetails_ReceivedUnits]    FOREIGN KEY ([ReceivedUnitId])  REFERENCES [dbo].[Units] ([UnitId]),
    CONSTRAINT [FK_GoodsReceivedNoteDetails_AcceptedUnits]    FOREIGN KEY ([AcceptedUnitId])  REFERENCES [dbo].[Units] ([UnitId]),

    CONSTRAINT [FK_GoodsReceivedNoteDetails_Locations]    FOREIGN KEY ([LocationId])	REFERENCES [dbo].[Locations] ([LocationId]),
    CONSTRAINT [FK_GoodsReceivedNoteDetails_Warehouses]   FOREIGN KEY ([WarehouseId])	REFERENCES [dbo].[Warehouses] ([WarehouseId]),
    CONSTRAINT [FK_GoodsReceivedNoteDetails_Stores]       FOREIGN KEY ([StoreId])	    REFERENCES [dbo].[Stores]    ([StoreId]),
    CONSTRAINT [FK_GoodsReceivedNoteDetails_Racks]        FOREIGN KEY ([RackId])	    REFERENCES [dbo].[Racks]     ([RackId]),
);
GO
CREATE NONCLUSTERED INDEX [GoodsReceivedNoteId]
    ON [dbo].[GoodsReceivedNoteDetails]([GoodsReceivedNoteId] ASC);
GO
CREATE NONCLUSTERED INDEX [PurchaseOrderDetailId]
    ON [dbo].[GoodsReceivedNoteDetails]([PurchaseOrderDetailId] ASC);
GO
CREATE NONCLUSTERED INDEX [Commodities]
    ON [dbo].[GoodsReceivedNoteDetails]([CommodityId] ASC);

GO
CREATE NONCLUSTERED INDEX [LocationId]
    ON [dbo].[GoodsReceivedNoteDetails]([LocationId] ASC);
GO
CREATE NONCLUSTERED INDEX [StoreId]
    ON [dbo].[GoodsReceivedNoteDetails]([StoreId] ASC);
GO
CREATE NONCLUSTERED INDEX [RackId]
    ON [dbo].[GoodsReceivedNoteDetails]([RackId] ASC);


