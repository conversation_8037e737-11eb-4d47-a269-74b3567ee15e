﻿using Serenity.Services;
using MyRequest = Serenity.Services.SaveRequest<ReconnBooks.Default.GoodsReceivedNoteTypesRow>;
using MyResponse = Serenity.Services.SaveResponse;
using MyRow = ReconnBooks.Default.GoodsReceivedNoteTypesRow;

namespace ReconnBooks.Default;

public interface IGoodsReceivedNoteTypesSaveHandler : ISaveHandler<MyRow, MyRequest, MyResponse> {}

public class GoodsReceivedNoteTypesSaveHandler : SaveRequestHandler<MyRow, MyRequest, MyResponse>, IGoodsReceivedNoteTypesSaveHandler
{
    public GoodsReceivedNoteTypesSaveHandler(IRequestContext context)
            : base(context)
    {
    }
}