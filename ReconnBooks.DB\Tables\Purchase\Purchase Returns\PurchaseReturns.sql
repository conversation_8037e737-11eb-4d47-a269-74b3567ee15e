﻿CREATE TABLE [dbo].[PurchaseReturns]
(
    [PurchaseReturnId]      INT				NOT NULL	IDENTITY (1, 1),
    [PurchaseReturnNo]      NVARCHAR (50)	NOT NULL,
    [PurchaseReturnDate]    DATETIME		    NULL,

    [VendorId]			    INT				NOT NULL,
    [PurchaseOrderId]		INT				    NULL,
    [GoodsReceivedNoteId]			        INT				    NULL,
    [FinancialYearId]		INT				    NULL,

    [UploadFiles]           NVARCHAR (500)	    NULL,
    [Remarks]		        NVARCHAR (MAX)	    NULL,

    -------------------Authorization Details-------------
    [ClientId]              INT	        NOT NULL    CONSTRAINT [DF_PurchaseReturns_ClientId]	DEFAULT ((0)),
    [PreparedByUserId]      INT	            NULL,
    [PreparedDate]          DATETIME        NULL,
    [VerifiedByUserId]      INT             NULL,
    [VerifiedDate]          DATETIME        NULL,
    [AuthorizedByUserId]    INT             NULL,
    [AuthorizedDate]        DATETIME        NULL,
    [ModifiedByUserId]      INT             NULL,
    [ModifiedDate]          DATETIME        NULL,
    [CancelledByUserId]     INT             NULL,
    [CancelledDate]			DATETIME        NULL,
    [AuthorizedStatus]      BIT         NOT NULL    DEFAULT ((0)),
   
   CONSTRAINT [PK_PurchaseReturns] PRIMARY KEY CLUSTERED ([PurchaseReturnId] ASC),
    CONSTRAINT [FK_PurchaseReturns_Clients] FOREIGN KEY ([ClientId]) REFERENCES [dbo].[Clients] ([ClientId]),
    CONSTRAINT [FK_PurchaseReturns_PreparedByUsers] FOREIGN KEY ([PreparedByUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_PurchaseReturns_VerfiedByUsers] FOREIGN KEY ([VerifiedByUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_PurchaseReturns_AuthorizedByUsers] FOREIGN KEY ([AuthorizedByUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_PurchaseReturns_ModifiedByUsers] FOREIGN KEY ([ModifiedByUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_PurchaseReturns_CancelledByUsers] FOREIGN KEY ([CancelledByUserId]) REFERENCES [dbo].[Users] ([UserId]),
    CONSTRAINT [FK_PurchaseReturns_FinancialYears] FOREIGN KEY ([FinancialYearId]) REFERENCES [dbo].[FinancialYears] ([FinancialYearId]),
    CONSTRAINT [FK_PurchaseReturns_Vendors] FOREIGN KEY ([VendorId]) REFERENCES [dbo].[Vendors] ([VendorId]),
    CONSTRAINT [FK_PurchaseReturns_PurchaseOrders] FOREIGN KEY ([PurchaseOrderId]) REFERENCES [dbo].[PurchaseOrders] ([PurchaseOrderId]),
    CONSTRAINT [FK_PurchaseReturns_GoodsReceivedNotes] FOREIGN KEY ([GoodsReceivedNoteId]) REFERENCES [dbo].[GoodsReceivedNotes] ([GoodsReceivedNoteId])
);

GO
CREATE NONCLUSTERED INDEX [VendorId]
    ON [dbo].[PurchaseReturns]([VendorId] ASC);

GO
CREATE NONCLUSTERED INDEX [PurchaseOrderId]
    ON [dbo].[PurchaseReturns]([PurchaseOrderId] ASC);

GO
CREATE NONCLUSTERED INDEX [GoodsReceivedNotes]
    ON [dbo].[PurchaseReturns]([GoodsReceivedNoteId] ASC);

GO
CREATE NONCLUSTERED INDEX [FinancialYears]
    ON [dbo].[PurchaseReturns]([FinancialYearId] ASC);

GO
CREATE NONCLUSTERED INDEX [Clients]
    ON [dbo].[PurchaseReturns]([ClientId] ASC);
